{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}
{% if type == 'choice' %}
    <div class="fv-rowd-flex flex-column">
        {% if not view_custom_property and not property.immutable and not line_item_property %}
        <div class="form-check form-switch form-check-custom form-check-solid">
            <input id="multiple-select-choice" name="multiple-select-choice" class="form-check-input" type="checkbox" {% if property.multiple_select or multiple_select_choice %} checked {% endif %}>
            <label class="form-check-label fw-semibold text-gray-700 ms-3" for="multiple-select-choice">
                {% if LANGUAGE_CODE == 'ja' %}
                複数選択
                {% else %}
                Multiple Select
                {% endif %}
            </label>
        </div>
        <div class="mt-4"></div>
        <div class="mb-6 form-check form-switch form-check-custom form-check-solid">
            <input id="choice-required-field" name="required-field" class="form-check-input" type="checkbox" {% if property.required_field or custom_property_value.required_field %} checked {% endif %}>
            <label class="form-check-label fw-semibold text-gray-700 ms-3" for="choice-required-field">
                {% if LANGUAGE_CODE == 'ja' %}
                必須フィールド
                {% else %}
                Required Field
                {% endif %}
            </label>
        </div>

        <div class="mb-6 form-check form-switch form-check-custom form-check-solid">
            <input id="choice-show-badges-field" name="show-badges" class="form-check-input" type="checkbox" {% if property.required_field or custom_property_value.required_field %} checked {% endif %}>
            <label class="form-check-label fw-semibold text-gray-700 ms-3" for="choice-required-field">
                {% if LANGUAGE_CODE == 'ja' %}
                バッジを表示
                {% else %}
                Show Badges
                {% endif %}
            </label>
        </div>

        {% endif %}
        <div class="mt-6 mb-3 d-flex w-100 {% if key %} d-none {% endif %} justify-content-between align-items-center">
            <div>
                <label class="fs-5 fw-bold mb-2">
                    <span class=""> 
                        {% if LANGUAGE_CODE == 'ja'%}
                        選択肢
                        {% else %}
                        Choices
                        {% endif %}
                    </span>
                </label>
                
                <a  class=" {% if page_group_type != 'conversation' %}
                                {% if not property.id|in_list:'inventory_status,slip_type,tax_rate,delivery_status,case_status,settle_choice,status,production_status,job_type,case_status' %}
                                    {% if property.immutable %}d-none{% endif %}
                                {% endif %}
                            {% endif %}"
                    href='javascript:;'
                    hx-get="{% host_url 'property_child' host 'app' %}" 
                    hx-vals = '{"type": "{{type}}", "page_group_type": "{{page_group_type}}", "add": "true"}'
                    hx-swap="beforeend"
                    hx-target="#choice-options"
                    hx-trigger="click"
                >
                    <span class="svg-icon svg-icon-primary svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <polygon points="0 0 24 0 24 24 0 24"/>
                            <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                            <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                        </g>
                    </span>
                    {% if LANGUAGE_CODE == 'ja'%}
                    選択肢を追加
                    {% else %}
                    Add Choice
                    {% endif %}

                </a> 

                <a  class="ms-4 {% if page_group_type != 'conversation' %}
                                    {% if not property.id|in_list:'slip_type,tax_rate,delivery_status,case_status,settle_choice,status,production_status,job_type,case_status' %}
                                        d-none
                                    {% endif %}
                                {% endif %}"
                    href='javascript:;'
                    hx-post="{% host_url 'property_child' host 'app' %}" 
                    hx-vals='{"type": "{{type}}", "page_group_type": "{{page_group_type}}", "add": "default" , "property_id": "{{property.id}}" }'
                    hx-swap="innerHTML"
                    hx-target="#choice-options"
                    hx-trigger="click"
                >
                    <span class="svg-icon svg-icon-primary svg-icon-2"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="m 8 0 c -4.40625 0 -8 3.59375 -8 8 s 3.59375 8 8 8 s 8 -3.59375 8 -8 s -3.59375 -8 -8 -8 z m 3.398438 4.507812 c 0.265624 -0.027343 0.527343 0.050782 0.734374 0.21875 c 0.425782 0.351563 0.488282 0.980469 0.140626 1.40625 l -4.5 5.5 c -0.179688 0.21875 -0.441407 0.351563 -0.722657 0.367188 c -0.28125 0.011719 -0.558593 -0.09375 -0.757812 -0.292969 l -2.5 -2.5 c -0.390625 -0.390625 -0.390625 -1.023437 0 -1.414062 s 1.023437 -0.390625 1.414062 0 l 1.71875 1.71875 l 3.800781 -4.644531 c 0.167969 -0.203126 0.410157 -0.335938 0.671876 -0.363282 z m 0 0" fill="#2e3436"></path> </g></svg>
                    </span>
                    
                    {% if LANGUAGE_CODE == 'ja'%}
                    デフォルトの選択
                    {% else %}
                    Default Choice
                    {% endif %}

                </a>

                {% comment %} property condition following "add choice"  {% endcomment %}
                <a class="ms-4 {% if page_group_type != 'conversation' %} 
                                    {% if not property.id|in_list:'inventory_status,slip_type,tax_rate,delivery_status,case_status,settle_choice,status,production_status,job_type,case_status' %}
                                        {% if property.immutable %}d-none{% endif %}
                                    {% endif %}
                                {% endif %}"
                    style="cursor:pointer"
                    onclick="this.querySelector('input[name=\'csv_file\']').click()">
                    <input type="file"
                           class="d-none"
                           accept=".csv"
                           name="csv_file"
                           hx-post="{% host_url 'property_child' host 'app' %}"
                           hx-vals='{"type": "{{type}}", "page_group_type": "{{page_group_type}}", "add": "csv_import", "property_id": "{{property.id}}"}'
                           hx-swap="innerHTML"
                           hx-target="#choice-options"
                           hx-encoding="multipart/form-data"
                           hx-trigger="change">
                        
                        <span class="svg-icon svg-icon-primary svg-icon-2"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="m 8 0 c -4.40625 0 -8 3.59375 -8 8 s 3.59375 8 8 8 s 8 -3.59375 8 -8 s -3.59375 -8 -8 -8 z m 3.398438 4.507812 c 0.265624 -0.027343 0.527343 0.050782 0.734374 0.21875 c 0.425782 0.351563 0.488282 0.980469 0.140626 1.40625 l -4.5 5.5 c -0.179688 0.21875 -0.441407 0.351563 -0.722657 0.367188 c -0.28125 0.011719 -0.558593 -0.09375 -0.757812 -0.292969 l -2.5 -2.5 c -0.390625 -0.390625 -0.390625 -1.023437 0 -1.414062 s 1.023437 -0.390625 1.414062 0 l 1.71875 1.71875 l 3.800781 -4.644531 c 0.167969 -0.203126 0.410157 -0.335938 0.671876 -0.363282 z m 0 0" fill="#2e3436"></path> </g></svg>
                        </span>
                        
                        {% if LANGUAGE_CODE == 'ja'%}
                        CSVをインポート
                        {% else %}
                        Import CSV
                        {% endif %}
                </a>

                <a class="ms-4 {% if page_group_type != 'conversation' %} 
                                    {% if not property.id|in_list:'inventory_status,slip_type,tax_rate,delivery_status,case_status,settle_choice,status,production_status,job_type,case_status' %}
                                        {% if property.immutable %}d-none{% endif %}
                                    {% endif %}
                                {% endif %}"
                    style="cursor:pointer"
                    name="export_csv_file"
                    hx-post="{% host_url 'property_child' host 'app' %}"
                    hx-vals='{"type": "{{type}}", "page_group_type": "{{page_group_type}}", "export": "csv_export", "property_id": "{{property.id}}"}'
                    hx-swap="innerHTML"
                    hx-target='.temp'
                    hx-trigger="click"
                    >
                    <span class="svg-icon svg-icon-primary svg-icon-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24">
                            <path d="M19,9h-4V3H9v6H5l7,7L19,9z M5,18v2h14v-2H5z" fill="currentColor"/>
                        </svg>
                    </span>

                    {% if LANGUAGE_CODE == 'ja'%}
                    エクスポート
                    {% else %}
                    Export
                    {% endif %}
                </a>
                <div class="temp"></div>
                
            </div>
            {% include 'data/property/property-search-key.html' with search_js="searchChoice(this)"%}
        </div>
    </div>

    <div id="response-div"></div>
    

    <script>
        function onUpdateCorresponding(num) {
            var income_ = document.getElementById('choice_label_income_' + num);
            var expense_ = document.getElementById('choice_label_expense_' + num);
            var choice_label = document.getElementById('choice_label_' + num);

            if (income_ && expense_) {
                if (choice_label){
                    choice_label.value = income_.value + ';' + expense_.value;
                }
            }
        }
    </script>


    <div id="choice-options">
        {% if page_group_type == 'journal' and property.id == 'counter_category' and property.choice_value and property.choice_value|to_str != '[{}]' %}
            <label class="w-25 me-20 fs-5 fw-bold mb-2">{% if LANGUAGE_CODE == 'ja'%}カテゴリ{% else %}Category{% endif %}</label>
            <label class="w-25 me-20 ms-5 fs-5 fw-bold mb-2">{% if LANGUAGE_CODE == 'ja'%}所得{% else %}Income{% endif %}</label>
            <label class="w-25 fs-5 fw-bold mb-2">{% if LANGUAGE_CODE == 'ja'%}費用{% else %}Expense{% endif %}</label>
        {% endif %}
        {% if is_custom_property %}
            {% for choice in property.value|string_list_to_list %}
                {% include 'data/property/property-choice-option.html' with choice=choice property=property %}
                {% empty %}
                {% include 'data/property/property-choice-option.html' %}
            {% endfor %}
        {% else %}
            {% for choice in property.choice_value|string_list_to_list %}
                {% include 'data/property/property-choice-option.html' with choice=choice property=property %}
                {% empty %}
                {% include 'data/property/property-choice-option.html' %}
            {% endfor %}
        {% endif %}
    </div>

    <script>
        var myElement = document.getElementById("choice-options");
        new Sortable(myElement, {
            animation: 150,
            ghostClass: 'blue-background-class',
            handle : '.task-grip'
        });

        

        function onChangeChoiceOption(inputElement) {
            const inputName = inputElement.name;
            const choiceValueElements = document.querySelectorAll(`input[name="${inputName}"]`);
            const choiceValues = Array.from(choiceValueElements).map(el => el.value);
        
            const currentChoiceValue = inputElement.value;
            inputElement.value = currentChoiceValue.replaceAll(';','')
            const warningLabel = inputElement.parentElement.nextElementSibling;
        
            if (choiceValues.filter(value => value === currentChoiceValue).length > 1) {
                {% if LANGUAGE_CODE == 'ja' %}
                    warningLabel.textContent = `オプション "${currentChoiceValue}" は既に存在しています。`;
                {% else %}
                    warningLabel.textContent = `Option "${currentChoiceValue}" already exists.`;
                {% endif %}
        
                warningLabel.style.display = 'block';
                inputElement.style.border = '1px solid red'; 
                
                {% if not from_table_mapping %}
                // Prevent form submission
                inputElement.form.onsubmit = function(event) {
                   event.preventDefault();
                };
                {% endif %}
            } else {
                warningLabel.textContent = '';
                warningLabel.style.display = 'none';
                inputElement.style.border = ''; 
                
                {% if not from_table_mapping %}
                // Allow form submission
                inputElement.form.onsubmit = null;
                {% endif %}
            }
        }               

        function handleFileSelect(input) {
            // Get all elements with the name "element-to-delete"
            const elementsToDelete = document.getElementsByName('choice_value');
            
            // Convert the HTMLCollection to an array and remove each element
            Array.from(elementsToDelete).forEach(element => {
                element.parentElement.remove();
            });
          }

        function triggerFileInput() {
            document.getElementById('csvFileInput').click();
          }
          
        function searchChoice(e) {
            var sub_section = document.querySelectorAll(".sub-section");

            // Getting Value
            var sub_choice_label = document.querySelectorAll(".sub-choice-label");
            var sub_choice_value = document.querySelectorAll(".sub-choice-value");

            const query = e.value.toLowerCase();
            console.log(query)

            if (query){
                for (var i = 0; i < sub_section.length; i++) {
                    sub_section[i].classList.add('d-none')
                }

                for (var i = 0; i < sub_section.length; i++) {
                    var choice_label = sub_choice_label[i].value.trim().toLowerCase();
                    var choice_value = sub_choice_value[i].value.trim().toLowerCase();

                    if (choice_label.includes(query) || choice_value.includes(query)){
                        sub_section[i].classList.remove('d-none')
                        sub_section[i].parentElement.parentElement.classList.remove('d-none')
                    }
                }

            }else{
                for (var i = 0; i < sub_section.length; i++) {
                    sub_section[i].classList.remove('d-none')
                }
            }
        }

    </script>

    {% if property %}
    <div class="{% include 'data/utility/form-label.html' %} cursor-pointer">
        <a id="choice-conditional-option" class="my-5 fs-7"
            hx-get="{% host_url 'property_choice_conditional_options' host 'app' %}"
            hx-vals='js:{"property_id":"{{property.id}}", "object_type": "{{page_group_type}}", "load_drawer": "true"}'
            hx-target="#view-settings-drawer"
            hx-swap="innerHTML"
            hx-trigger="click"
        >
            <span class="svg-icon svg-icon-primary svg-icon-2x">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                    <path opacity="0.3" d="M18.4 5.59998C18.7766 5.9772 18.9881 6.48846 18.9881 7.02148C18.9881 7.55451 18.7766 8.06577 18.4 8.44299L14.843 12C14.466 12.377 13.9547 12.5887 13.4215 12.5887C12.8883 12.5887 12.377 12.377 12 12C11.623 11.623 11.4112 11.1117 11.4112 10.5785C11.4112 10.0453 11.623 9.53399 12 9.15698L15.553 5.604C15.9302 5.22741 16.4415 5.01587 16.9745 5.01587C17.5075 5.01587 18.0188 5.22741 18.396 5.604L18.4 5.59998ZM20.528 3.47205C20.0614 3.00535 19.5074 2.63503 18.8977 2.38245C18.288 2.12987 17.6344 1.99988 16.9745 1.99988C16.3145 1.99988 15.661 2.12987 15.0513 2.38245C14.4416 2.63503 13.8876 3.00535 13.421 3.47205L9.86801 7.02502C9.40136 7.49168 9.03118 8.04568 8.77863 8.6554C8.52608 9.26511 8.39609 9.91855 8.39609 10.5785C8.39609 11.2384 8.52608 11.8919 8.77863 12.5016C9.03118 13.1113 9.40136 13.6653 9.86801 14.132C10.3347 14.5986 10.8886 14.9688 11.4984 15.2213C12.1081 15.4739 12.7616 15.6039 13.4215 15.6039C14.0815 15.6039 14.7349 15.4739 15.3446 15.2213C15.9543 14.9688 16.5084 14.5986 16.975 14.132L20.528 10.579C20.9947 10.1124 21.3649 9.55844 21.6175 8.94873C21.8701 8.33902 22.0001 7.68547 22.0001 7.02551C22.0001 6.36555 21.8701 5.71201 21.6175 5.10229C21.3649 4.49258 20.9947 3.93867 20.528 3.47205Z" fill="currentColor"/>
                    <path d="M14.132 9.86804C13.6421 9.37931 13.0561 8.99749 12.411 8.74695L12 9.15698C11.6234 9.53421 11.4119 10.0455 11.4119 10.5785C11.4119 11.1115 11.6234 11.6228 12 12C12.3766 12.3772 12.5881 12.8885 12.5881 13.4215C12.5881 13.9545 12.3766 14.4658 12 14.843L8.44699 18.396C8.06999 18.773 7.55868 18.9849 7.02551 18.9849C6.49235 18.9849 5.98101 18.773 5.604 18.396C5.227 18.019 5.0152 17.5077 5.0152 16.9745C5.0152 16.4413 5.227 15.93 5.604 15.553L8.74701 12.411C8.28705 11.233 8.28705 9.92498 8.74701 8.74695C8.10159 8.99737 7.5152 9.37919 7.02499 9.86804L3.47198 13.421C2.52954 14.3635 2.00009 15.6417 2.00009 16.9745C2.00009 18.3073 2.52957 19.5855 3.47202 20.528C4.41446 21.4704 5.69269 21.9999 7.02551 21.9999C8.35833 21.9999 9.63656 21.4704 10.579 20.528L14.132 16.975C14.5987 16.5084 14.9689 15.9544 15.2215 15.3447C15.4741 14.735 15.6041 14.0815 15.6041 13.4215C15.6041 12.7615 15.4741 12.108 15.2215 11.4983C14.9689 10.8886 14.5987 10.3347 14.132 9.86804Z" fill="currentColor"/>
                </svg>
            </span>
            {% if LANGUAGE_CODE == 'ja'%}
                条件別表示設定
            {% else %}
                Conditional Display Settings
            {% endif %}
        </a>
    </div>
    {% endif%}

{% elif type == 'contact_list' %}

    <div class="mt-6 mb-3 d-flex w-100 justify-content-between align-items-center">
        <div>
            
            <a  
                href='javascript:;'
                hx-get="{% host_url 'property_child' host 'app' %}" 
                hx-vals = '{"type": "{{type}}", "page_group_type": "{{page_group_type}}", "add": "true"}'
                hx-swap="beforeend"
                hx-target="#choice-options"
                hx-trigger="click"
            >
                <span class="svg-icon svg-icon-primary svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <polygon points="0 0 24 0 24 24 0 24"/>
                        <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                        <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                    </g>
                </span>
                {% if LANGUAGE_CODE == 'ja'%}
                連絡先リストを追加
                {% else %}
                Add Contact List
                {% endif %}

            </a> 
            
        </div>

    </div>

    <div id='choice-options'>

        {% for choice in property.choice_value|string_list_to_list %}
            {% include 'data/property/property-contact-list.html' with choice=choice property=property %}
        {% empty %}
            {% include 'data/property/property-contact-list.html' %}
        {% endfor %}

    </div>

    


{% elif type == 'hierarchy' %}
    <div class="fv-rowd-flex flex-column">
        <div class="mt-6 mb-3 d-flex w-100 {% if key %} d-none {% endif %} justify-content-between align-items-center">
            <div>
                <label class="fs-5 fw-bold mb-2">
                    <span class=""> 
                        {% if LANGUAGE_CODE == 'ja'%}
                        階層
                        {% else %}
                        Hierarchy
                        {% endif %}
                    </span>
                </label>
                
                <a  class=""
                    href='javascript:;'
                    hx-get="{% host_url 'property_child' host 'app' %}" 
                    hx-vals = '{"type": "{{type}}", "page_group_type": "{{page_group_type}}", "add": "true"}'
                    hx-swap="beforeend"
                    hx-target="#choice-options"
                    hx-trigger="click"
                >
                    <span class="svg-icon svg-icon-primary svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <polygon points="0 0 24 0 24 24 0 24"/>
                            <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                            <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                        </g>
                    </span>
                    {% if LANGUAGE_CODE == 'ja'%}
                    階層を追加
                    {% else %}
                    Add Hierarchy
                    {% endif %}

                </a> 
            </div>
            {% include 'data/property/property-search-key.html' with search_js="searchChoice(this)"%}
        </div>
    </div>

    <div id="response-div"></div>

    <script>
        function onUpdateCorresponding(num) {
            var income_ = document.getElementById('choice_label_income_' + num);
            var expense_ = document.getElementById('choice_label_expense_' + num);
            var choice_label = document.getElementById('choice_label_' + num);

            if (income_ && expense_) {
                if (choice_label){
                    choice_label.value = income_.value + ';' + expense_.value;
                }
            }
        }
    </script>


    <div id="choice-options">
        {% for choice in property.choice_value|string_list_to_list %}
            {% include 'data/property/property-hierarchy-option.html' with choice=choice property=property %}
        {% empty %}
            {% include 'data/property/property-hierarchy-option.html' %}
        {% endfor %}
    </div>

    <script>
        var myElement = document.getElementById("choice-options");
        new Sortable(myElement, {
            animation: 150,
            ghostClass: 'blue-background-class',
            handle : '.task-grip'
        });

        

        function onChangeChoiceOption(inputElement) {
            const inputName = inputElement.name;
            const choiceValueElements = document.querySelectorAll(`input[name="${inputName}"]`);
            const choiceValues = Array.from(choiceValueElements).map(el => el.value);
        
            const currentChoiceValue = inputElement.value;
            inputElement.value = currentChoiceValue.replaceAll(';','')
            const warningLabel = inputElement.parentElement.nextElementSibling;
        
            if (choiceValues.filter(value => value === currentChoiceValue).length > 1) {
                {% if LANGUAGE_CODE == 'ja' %}
                    warningLabel.textContent = `オプション "${currentChoiceValue}" は既に存在しています。`;
                {% else %}
                    warningLabel.textContent = `Option "${currentChoiceValue}" already exists.`;
                {% endif %}
        
                warningLabel.style.display = 'block';
                inputElement.style.border = '1px solid red'; 
                
                {% if not from_table_mapping %}
                // Prevent form submission
                inputElement.form.onsubmit = function(event) {
                   event.preventDefault();
                };
                {% endif %}
            } else {
                warningLabel.textContent = '';
                warningLabel.style.display = 'none';
                inputElement.style.border = ''; 
                
                {% if not from_table_mapping %}
                // Allow form submission
                inputElement.form.onsubmit = null;
                {% endif %}
            }
        }               

        function handleFileSelect(input) {
            // Get all elements with the name "element-to-delete"
            const elementsToDelete = document.getElementsByName('choice_value');
            
            // Convert the HTMLCollection to an array and remove each element
            Array.from(elementsToDelete).forEach(element => {
                element.parentElement.remove();
            });
          }

        function triggerFileInput() {
            document.getElementById('csvFileInput').click();
          }
          
        function searchChoice(e) {
            var sub_section = document.querySelectorAll(".sub-section");

            // Getting Value
            var sub_choice_label = document.querySelectorAll(".sub-choice-label");
            var sub_choice_value = document.querySelectorAll(".sub-choice-value");

            const query = e.value.toLowerCase();
            console.log(query)

            if (query){
                for (var i = 0; i < sub_section.length; i++) {
                    sub_section[i].classList.add('d-none')
                }

                for (var i = 0; i < sub_section.length; i++) {
                    var choice_label = sub_choice_label[i].value.trim().toLowerCase();
                    var choice_value = sub_choice_value[i].value.trim().toLowerCase();

                    if (choice_label.includes(query) || choice_value.includes(query)){
                        sub_section[i].classList.remove('d-none')
                        sub_section[i].parentElement.parentElement.classList.remove('d-none')
                    }
                }

            }else{
                for (var i = 0; i < sub_section.length; i++) {
                    sub_section[i].classList.remove('d-none')
                }
            }
        }

    </script>

{% elif type == 'process_master' %}
    <div class="mt-6 mb-3 d-flex w-100">
        <label class="fs-5 fw-bold mb-2">
            <span class=""> 
                {% if LANGUAGE_CODE == 'ja'%}
                プロセス
                {% else %}
                Processes
                {% endif %}
            </span>
        </label>
        
        <a  class=" {% if page_group_type != 'conversation' %}
                        {% if property.id != 'slip_type' and property.id != 'delivery_status' and property.id != 'case_status' and property.id != 'settle_choice' and property.id != 'status' and property.id != 'production_status' %}
                            {% if property.immutable %}d-none{% endif %}
                        {% endif %}
                    {% endif %}"
            href='javascript:;'
            hx-get="{% host_url 'property_child' host 'app' %}" 
            hx-vals = '{"type": "{{type}}", "page_group_type": "{{page_group_type}}", "add": "true"}'
            hx-swap="beforeend"
            hx-target="#process-master-options"
            hx-trigger="click"
        >
            <span class="svg-icon svg-icon-primary svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <polygon points="0 0 24 0 24 24 0 24"/>
                    <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                    <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                </g>
            </span>
            {% if LANGUAGE_CODE == 'ja'%}
            プロセスを追加
            {% else %}
            Add Process
            {% endif %}

        </a>

        {% comment %} <span class="fw-bolder {% if page_group_type != 'conversation' %}
                            {% if property.id != 'delivery_status' and property.id != 'case_status' and property.id != 'settle_choice' and property.id != 'status'  and property.id != 'production_status' %}
                                    {% if property.immutable %}d-none{% endif %}
                                {% endif %}
                                {% endif %} ">
            <input 
                type="file" 
                id="csvFileInput" 
                name="csv_file" 
                accept=".csv" 
                style="display: none;"
                hx-post="{% host_url 'property_child' host 'app' %}"
                hx-vals='{"type": "{{type}}", "page_group_type": "{{page_group_type}}","add": "true"  }'
                hx-encoding="multipart/form-data"
                hx-trigger="change"
                hx-target="#response-div"
                onchange="handleFileSelect(this)"
            >

            <button  onclick="triggerFileInput()" type="button" class="max-md:tw-hidden tw-block align-items-center d-flex btn btn-md py-1 px-2 fs-7 justify-content-center" style="cursor: pointer;">
                <span class="svg-icon svg-icon-primary svg-icon-2x4">
                    <svg xmlns="http://www.w3.-listorg/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M21 22H3C2.4 22 2 21.6 2 21C2 20.4 2.4 20 3 20H21C21.6 20 22 20.4 22 21C22 21.6 21.6 22 21 22ZM13 13.4V3C13 2.4 12.6 2 12 2C11.4 2 11 2.4 11 3V13.4H13Z" fill="black"/>
                        <path opacity="0.3" d="M7 13.4H17L12.7 17.7C12.3 18.1 11.7 18.1 11.3 17.7L7 13.4Z" fill="black"/>
                    </svg>
                </span>
                <span class="text-primary">
                    {% if LANGUAGE_CODE == 'ja'%}
                    CSVで工程マスタをアップロード
                    {% else %}
                    Upload Process Master with CSV
                    {% endif %}
                </span>
            </button>
            
        </span> {% endcomment %}



    </div>

    </div>

    <div id="response-div"></div>

    <div id="process-master-options">
    {% for choice in property.choice_value|string_list_to_list %}
        {% include 'data/property/property-choice-option.html' with choice=choice property=property %}
    {% empty %}
        {% include 'data/property/property-choice-option.html' %}
    {% endfor %}
    </div>


    <script>
    var myElement = document.getElementById("process-master-options");
    new Sortable(myElement, {
        animation: 150,
        ghostClass: 'blue-background-class',
        handle : '.task-grip'
    });

    function onChangeChoiceOption(inputElement) {
        const inputName = inputElement.name;
        const choiceValueElements = document.querySelectorAll(`input[name="${inputName}"]`);
        const choiceValues = Array.from(choiceValueElements).map(el => el.value);

        const currentChoiceValue = inputElement.value;
        inputElement.value = currentChoiceValue.replaceAll(';','')
        const warningLabel = inputElement.parentElement.nextElementSibling;

        if (choiceValues.filter(value => value === currentChoiceValue).length > 1) {
            {% if LANGUAGE_CODE == 'ja' %}
                warningLabel.textContent = `オプション "${currentChoiceValue}" は既に存在しています。`;
            {% else %}
                warningLabel.textContent = `Option "${currentChoiceValue}" already exists.`;
            {% endif %}

            warningLabel.style.display = 'block';
            inputElement.style.border = '1px solid red'; 
            
            // Prevent form submission
            inputElement.form.onsubmit = function(event) {
                event.preventDefault();
            };
        } else {
            warningLabel.textContent = '';
            warningLabel.style.display = 'none';
            inputElement.style.border = ''; 
            
            // Allow form submission
            inputElement.form.onsubmit = null;
        }
    }               

    function handleFileSelect(input) {
        // Get all elements with the name "element-to-delete"
        const elementsToDelete = document.getElementsByName('choice_value');
        
        // Convert the HTMLCollection to an array and remove each element
        Array.from(elementsToDelete).forEach(element => {
            element.parentElement.remove();
        });
    }
    function triggerFileInput() {
        document.getElementById('csvFileInput').click();
    }
    </script>


{% elif type == 'hierarchy_choice' %}
    <div class="fv-rowd-flex flex-column">
        <div class="mt-6 mb-3 d-flex w-100 justify-content-between align-items-center">
            <div>

                <div class="mt-4"></div>
                <div class="mb-6 form-check form-switch form-check-custom form-check-solid">
                    <input id="hierarchy-choice-required-field" name="required-field" class="form-check-input" type="checkbox" {% if property.required_field or custom_property_value.required_field %} checked {% endif %}>
                    <label class="form-check-label fw-semibold text-gray-700 ms-3" for="hierarchy-choice-required-field">
                        {% if LANGUAGE_CODE == 'ja' %}
                        必須フィールド
                        {% else %}
                        Required Field
                        {% endif %}
                    </label>
                </div>

                <label class="fs-5 fw-bold mb-2">
                    <span class=""> 
                        {% if LANGUAGE_CODE == 'ja'%}
                        グループ選択肢
                        {% else %}
                        Group Choice
                        {% endif %}
                    </span>
                </label>
                
                <a  class=""
                    href='javascript:;'
                    hx-get="{% host_url 'property_child' host 'app' %}" 
                    hx-vals = '{"type": "{{type}}","choice": "main_category", "page_group_type": "{{page_group_type}}", "add": "true"}'
                    hx-swap="beforeend"
                    hx-target="#hierarchy-choice-options"
                    hx-trigger="click"
                >
                    <span class="svg-icon svg-icon-primary svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <polygon points="0 0 24 0 24 24 0 24"/>
                            <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                            <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                        </g>
                    </span>
                    {% if LANGUAGE_CODE == 'ja'%}
                    グループを追加
                    {% else %}
                    Add Group
                    {% endif %}

                </a>

                {% if page_group_type == 'journal' %}
                <a  class="ms-4"
                    href='javascript:;'
                    hx-post="{% host_url 'property_child' host 'app' %}" 
                    hx-vals='{"type": "{{type}}", "page_group_type": "{{page_group_type}}", "add": "default" , "property_id": "{{property.id}}" }'
                    hx-swap="innerHTML"
                    hx-target="#hierarchy-choice-options"
                    hx-trigger="click"
                >
                    <span class="svg-icon svg-icon-primary svg-icon-2"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="m 8 0 c -4.40625 0 -8 3.59375 -8 8 s 3.59375 8 8 8 s 8 -3.59375 8 -8 s -3.59375 -8 -8 -8 z m 3.398438 4.507812 c 0.265624 -0.027343 0.527343 0.050782 0.734374 0.21875 c 0.425782 0.351563 0.488282 0.980469 0.140626 1.40625 l -4.5 5.5 c -0.179688 0.21875 -0.441407 0.351563 -0.722657 0.367188 c -0.28125 0.011719 -0.558593 -0.09375 -0.757812 -0.292969 l -2.5 -2.5 c -0.390625 -0.390625 -0.390625 -1.023437 0 -1.414062 s 1.023437 -0.390625 1.414062 0 l 1.71875 1.71875 l 3.800781 -4.644531 c 0.167969 -0.203126 0.410157 -0.335938 0.671876 -0.363282 z m 0 0" fill="#2e3436"></path> </g></svg>
                    </span>
                    
                    {% if LANGUAGE_CODE == 'ja'%}
                    デフォルトの選択
                    {% else %}
                    Default Choice
                    {% endif %}

                </a>
                {% include 'data/property/property-search-key.html' with search_js="searchGroupChoice(this)" %}
                {% endif %}

                
                {% comment %} property condition following "add choice"  {% endcomment %}
                <a class="ms-4 {% if page_group_type != 'conversation' %} 
                                    {% if not property.id|in_list:'inventory_status,slip_type,tax_rate,delivery_status,case_status,settle_choice,status,production_status,job_type,case_status' %}
                                        {% if property.immutable %}d-none{% endif %}
                                    {% endif %}
                                {% endif %}"
                    style="cursor:pointer"
                    onclick="this.querySelector('input[name=\'csv_file\']').click()">
                    <input type="file"
                           class="d-none"
                           accept=".csv"
                           name="csv_file"
                           hx-post="{% host_url 'property_child' host 'app' %}"
                           hx-vals='{"type": "{{type}}", "page_group_type": "{{page_group_type}}", "add": "csv_import", "property_id": "{{property.id}}"}'
                           hx-swap="innerHTML"
                           hx-target="#hierarchy-choice-options"
                           hx-encoding="multipart/form-data"
                           hx-trigger="change">
                        
                        <span class="svg-icon svg-icon-primary svg-icon-2"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="m 8 0 c -4.40625 0 -8 3.59375 -8 8 s 3.59375 8 8 8 s 8 -3.59375 8 -8 s -3.59375 -8 -8 -8 z m 3.398438 4.507812 c 0.265624 -0.027343 0.527343 0.050782 0.734374 0.21875 c 0.425782 0.351563 0.488282 0.980469 0.140626 1.40625 l -4.5 5.5 c -0.179688 0.21875 -0.441407 0.351563 -0.722657 0.367188 c -0.28125 0.011719 -0.558593 -0.09375 -0.757812 -0.292969 l -2.5 -2.5 c -0.390625 -0.390625 -0.390625 -1.023437 0 -1.414062 s 1.023437 -0.390625 1.414062 0 l 1.71875 1.71875 l 3.800781 -4.644531 c 0.167969 -0.203126 0.410157 -0.335938 0.671876 -0.363282 z m 0 0" fill="#2e3436"></path> </g></svg>
                        </span>
                        
                        {% if LANGUAGE_CODE == 'ja'%}
                        CSVをインポート
                        {% else %}
                        Import CSV
                        {% endif %}
                </a>

                <a class="ms-4 {% if page_group_type != 'conversation' %} 
                                    {% if not property.id|in_list:'inventory_status,slip_type,tax_rate,delivery_status,case_status,settle_choice,status,production_status,job_type,case_status' %}
                                        {% if property.immutable %}d-none{% endif %}
                                    {% endif %}
                                {% endif %}"
                    style="cursor:pointer"
                    name="export_csv_file"
                    hx-post="{% host_url 'property_child' host 'app' %}"
                    hx-vals='{"type": "{{type}}", "page_group_type": "{{page_group_type}}", "export": "csv_export", "property_id": "{{property.id}}"}'
                    hx-swap="innerHTML"
                    hx-target='.temp'
                    hx-trigger="click"
                    >
                    <span class="svg-icon svg-icon-primary svg-icon-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24">
                            <path d="M19,9h-4V3H9v6H5l7,7L19,9z M5,18v2h14v-2H5z" fill="currentColor"/>
                        </svg>
                    </span>

                    {% if LANGUAGE_CODE == 'ja'%}
                    エクスポート
                    {% else %}
                    Export
                    {% endif %}
                </a>
                <div class="temp"></div>


            </div>
            
            



        </div>

    </div>

    <div id="response-div"></div>

    <div id="hierarchy-choice-options">
        {% for choices in property.choice_value|string_list_to_list %}
            {% include 'data/property/property-hierarchy-main-category.html' with choices=choices property=property uid=forloop.counter %}
        {% empty %}
            {% include 'data/property/property-hierarchy-main-category.html' %}
        {% endfor %}
    </div>


    <script>
        var myElement = document.getElementById("hierarchy-choice-options");
        new Sortable(myElement, {
            animation: 150,
            ghostClass: 'blue-background-class',
            handle : '.title-task-grip'
        });

        function searchGroupChoice(e) {
            var main_section = document.querySelectorAll(".main-section");
            var main_choice_section = document.querySelectorAll(".main-choice-section");
            var sub_section = document.querySelectorAll(".sub-section");

            // Getting Value
            var main_choice = document.querySelectorAll(".main-choice");
            var sub_choice_label = document.querySelectorAll(".sub-choice-label");
            var sub_choice_value = document.querySelectorAll(".sub-choice-value");

            const query = e.value.toLowerCase();
            console.log(query)

            if (query){
                for (var i = 0; i < main_section.length; i++) {
                    main_section[i].classList.add('d-none')
                }
                for (var i = 0; i < sub_section.length; i++) {
                    sub_section[i].classList.add('d-none')
                }

                
                for (var i = 0; i < main_choice.length; i++) {
                    var choice_value = main_choice[i].value.trim().toLowerCase();
                    if (choice_value.includes(query)){
                        main_section[i].classList.remove('d-none')
                    }
                }
                for (var i = 0; i < sub_section.length; i++) {
                    var choice_label = sub_choice_label[i].value.trim().toLowerCase();
                    var choice_value = sub_choice_value[i].value.trim().toLowerCase();
                    if (choice_label.includes(query) || choice_value.includes(query)){
                        sub_section[i].classList.remove('d-none')
                        sub_section[i].parentElement.parentElement.classList.remove('d-none')
                    }
                }

            }else{
                for (var i = 0; i < main_section.length; i++) {
                    main_section[i].classList.remove('d-none')
                }
                for (var i = 0; i < sub_section.length; i++) {
                    sub_section[i].classList.remove('d-none')
                }
            }
        }
        function onChangeChoiceOption(inputElement) {
            const inputName = inputElement.name;
            const choiceValueElements = document.querySelectorAll(`input[name^="choice_value_"]`);
            const choiceValues = Array.from(choiceValueElements).map(el => el.value);
        
            const currentChoiceValue = inputElement.value;
            inputElement.value = currentChoiceValue.replaceAll(';','')
            const warningLabel = inputElement.parentElement.nextElementSibling;
        
            if (choiceValues.filter(value => value === currentChoiceValue).length > 1) {
                {% if LANGUAGE_CODE == 'ja' %}
                    warningLabel.textContent = `オプション "${currentChoiceValue}" は既に存在しています。`;
                {% else %}
                    warningLabel.textContent = `Option "${currentChoiceValue}" already exists.`;
                {% endif %}
        
                warningLabel.style.display = 'block';
                inputElement.style.border = '1px solid red'; 
                
                // Prevent form submission
                inputElement.form.onsubmit = function(event) {
                    event.preventDefault();
                };
            } else {
                warningLabel.textContent = '';
                warningLabel.style.display = 'none';
                inputElement.style.border = ''; 
                
                // Allow form submission
                inputElement.form.onsubmit = null;
            }
        }               

        function handleFileSelect(input) {
            // Get all elements with the name "element-to-delete"
            const elementsToDelete = document.getElementsByName('choice_value');
            
            // Convert the HTMLCollection to an array and remove each element
            Array.from(elementsToDelete).forEach(element => {
                element.parentElement.remove();
            });
          }

          function triggerFileInput() {
            document.getElementById('csvFileInput').click();
          }

    </script>


{% elif type == 'number' %}
    <div class="mb-3">
        <label class="fs-5 fw-bold mb-2">
            <span class=""> 
                {% if LANGUAGE_CODE == 'ja'%}
                フォーマット
                {% else %}
                Format
                {% endif %}
            </span>
        </label>

        <select
            class="h-40px form-select form-select-solid border bg-white select2-this"
            data-control="select2" 
            name="number_format"
            data-allow-clear="true">
                {% for number_format in number_formats %} 
                    <option value="{{ number_format.0 }}" 
                        {% if property.number_format == number_format.0 %}selected{% endif %}
                    >
                        
                            {% if number_format.0 == 'number' %}
                                {% if LANGUAGE_CODE == 'ja' %}
                                数値
                                {% else %}
                                {{  number_format.1 }}
                                {% endif %}
                            {% else %}
                                {{  number_format.1 }}
                            {% endif %}
                        
                    </option>
            {% endfor %}
        </select>
    </div>

    <div class="mb-6 form-check form-switch form-check-custom form-check-solid">
        <input id="number-required-field" name="required-field" class="form-check-input" type="checkbox" {% if property.required_field or custom_property_value.required_field %} checked {% endif %}>
        <label class="form-check-label fw-semibold text-gray-700 ms-3" for="number-required-field">
            {% if LANGUAGE_CODE == 'ja' %}
            必須フィールド
            {% else %}
            Required Field
            {% endif %}
        </label>
    </div>


{% elif type == 'formula' %}
    
    <div class="mb-3">
        <label class="fs-5 fw-bold mb-3">
            <span class=""> 
                {% if LANGUAGE_CODE == 'ja'%}
                表示
                {% else %}
                Display
                {% endif %}
            </span>
        </label>
        <div>
            <select class="h-40px form-select form-select-solid border select2-this bg-white"
                data-control="select2" name="value_display">
                <option value="text" {% if property.value_display == 'text' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja'%}テキスト{% else %}Text{% endif %}</option>
                <option value="number" {% if property.value_display == 'number' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja'%}数値{% else %}Number{% endif %}</option>
                <option value="percent" {% if property.value_display == 'percent' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja'%}パーセント{% else %}Percent (%){% endif %}</option>
                    {% for display_format in display_formats %} 
                        <option value="{{ display_format.0 }}" 
                            {% if property.value_display == display_format.0 %}selected{% endif %}
                            >
                                {% if display_format.0 == 'number' %}
                                    {% if LANGUAGE_CODE == 'ja' %}
                                    数値
                                    {% else %}
                                    {{  display_format.1 }}
                                    {% endif %}
                                {% else %}
                                    {{  display_format.1 }}
                                {% endif %}
                        </option>
                    {% endfor %}
            </select>
        </div>
    </div>
    <div class="tags-container mb-4 min-h-40px rounded-3 border" id="tags-container">
        <input autocomplete="off" type="text" id="tag-input" 

            {% if LANGUAGE_CODE == 'ja'%}
            placeholder="数式を挿入" 
            {% else %}
            placeholder="Insert Formula" 
            {% endif %}
            
            >
        <div class="suggestions" id="suggestions"></div>
    </div>
    <input type="hidden" id="hidden-input" name="choice_value" >

    <div  class="mb-3">
        <label class="fs-5 fw-bold mb-3">
            <span class=""> 
                {% if LANGUAGE_CODE == 'ja'%}
                バッジ
                {% else %}
                Badge
                {% endif %}
            </span>
        </label>
        <input type="color" class="form-control form-control-color" style="width: 60px; height: 40px;" name="choice_color"
            {% if LANGUAGE_CODE == 'ja'%}
            title="色を選択"
            {% else %}
            title="Choose color"
            {% endif %}
            value="{{choice.color|default:'#000000'}}"
        />
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@yaireo/dragsort/dist/dragsort.js"></script>

    <style>
        .tags-container {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            position: relative;
        }
        .tag {
            display: flex;
            align-items: center;
            background-color: #f5f5f5;
            border-radius: 4px;
            padding: 2px 8px;
            margin: 2px;
            cursor: move;
        }
        .tag-remove {
            margin-left: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        #tag-input {
            border: none;
            outline: none;
            flex-grow: 1;
            min-width: 50px;
        }
        .suggestions {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background-color: white;
            border: 1px solid #ccc;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
        }
        .suggestion-group {
            border-bottom: 1px solid #eee;
        }
        .suggestion-group:last-child {
            border-bottom: none;
        }
        .group-title {
            font-weight: bold;
            padding: 5px 10px;
            background-color: #f5f5f5;
        }
        .suggestion-item {
            padding: 5px 10px;
            cursor: pointer;
        }
        .suggestion-item:hover {
            background-color: #f0f0f0;
        }
    </style>

    <script>

        $(document).ready(function() {

            var tagsContainer = document.getElementById('tags-container');
            var tagInput = document.getElementById('tag-input');
            var suggestionsContainer = document.getElementById('suggestions');
            var hiddenInput = document.getElementById('hidden-input');
            var tags = [];

            // Initialize dragsort
            var dragsort = new DragSort(tagsContainer, {
                selector: '.tag',
                callbacks: {
                    dragEnd: function() {
                        // Reorder tags array based on DOM order
                        var newTags = [];
                        tagsContainer.querySelectorAll('.tag').forEach(function(tagElement) {
                            var tagValue = tagElement.getAttribute('data-value');
                            var tag = tags.find(t => t.value === tagValue);
                            if (tag) newTags.push(tag);
                        });
                        tags = newTags;
                        updateHiddenInput();
                    }
                }
            });


            // ========================= Create Suggestion

            var groupedSuggestions = {
                'Operator': [
                    { display: '+', value: '+', group: 'Operator' },
                    { display: '-', value: '-', group: 'Operator' },
                    { display: '*', value: '*', group: 'Operator' },
                    { display: '/', value: '/', group: 'Operator' },
                    { display: '(', value: '(', group: 'Operator' },
                    { display: ')', value: ')', group: 'Operator' },
                    { display: ',', value: ',', group: 'Operator' }
                ],
            };
       
            {% for property_ in functions_properties %}
                groupedSuggestions.Operator.push({ display: '{{property_.id|title}}', value: '{{property_.id}}', group: 'Operator' })
            {% endfor %}
            
            groupedSuggestions['Property - Number']=[]
            {% for property_ in default_number_properties %}
                groupedSuggestions['Property - Number'].push({ display: '{% read_display_column_with_request property_ default_property_column_display %}', value: '{{property_}}', group: 'Property' })
            {% endfor %}

            {% if page_group_type == 'commerce_inventory_warehouse'%}
                {% for property_ in relation_properties %}
                    groupedSuggestions['Property - Number'].push({ display: "{{property_.name}}", value: "{{property_.id}}", group: "Property" })
                {% endfor %}
            {% elif page_group_type == 'production' %}
                {% for property_ in relation_properties %}
                    {% if property_.type == "date" or property_.type == "number" %}
                        groupedSuggestions['Property - Number'].push({ display: "{{property_.name}}", value: "{{property_.id}}", group: "Property" })
                    {% endif %}
                {% endfor %}
            {% else%}
                {% if relation_properties|model_filter:"{'type':'number'}" %}
                    {% for property_ in relation_properties %}
                        {% if property_.type == "number" %}
                            groupedSuggestions['Property - Number'].push({ display: "{{property_.name}}", value: "{{property_.id}}", group: "Property" })
                        {% endif %}
                    {% endfor %}
                {% endif %}
            {% endif %}
            
            groupedSuggestions['Property - Text']=[]
            {% for property_ in default_text_properties %}
                groupedSuggestions['Property - Text'].push({ display: '{% read_display_column_with_request property_ default_property_column_display %}', value: '{{property_}}', group: 'Property' })
            {% endfor %}
            
            {% for property_ in custom_text_properties %}
                groupedSuggestions['Property - Text'].push({ display: '{{property_.name}}', value: '{{property_.id}}', group: 'Property' })
            {% endfor %}

            groupedSuggestions['Custom Property - Number']=[]
            {% for property_ in object_properties_number %}
                groupedSuggestions['Custom Property - Number'].push({ display: '{{property_.value}}', value: '{{property_.id}}', group: 'Property' })
            {% endfor %}

            groupedSuggestions['Related Data']=[]
            {% if related_item_properties or related_data %}
                {% if page_group_type == 'production' %}
                    {% for property_ in related_item_properties %}
                        {% if LANGUAGE_CODE == 'ja' %}
                            groupedSuggestions['Related Data'].push({ display: "最大値( 商品プロパティ - {{property_.name}} )", value: "{{property_.id}}|item_property|max", group: "Related Data" })
                            groupedSuggestions['Related Data'].push({ display: "最小値( 商品プロパティ - {{property_.name}} )", value: "{{property_.id}}|item_property|min", group: "Related Data" })
                        {% else %}
                            groupedSuggestions['Related Data'].push({ display: "Max( Item Property  - {{property_.name}} )", value: "{{property_.id}}|item_property|max", group: "Related Data" })
                            groupedSuggestions['Related Data'].push({ display: "Min( Item Property  - {{property_.name}} )", value: "{{property_.id}}|item_property|min", group: "Related Data" })
                        {% endif %}
                    {% endfor %}
                {% else %}
                    {% for property_ in related_data %}
                    groupedSuggestions['Related Data'].push({
                        
                    {% if '|amount|' in property_.value and 'journal' in property_.value %}
                    {% else %}
                        {% if property_.sub_property_name and 'sum' in property_.value %}
                            display: "SUM({{property_.object_name}} - {{property_.property_name}} - {{property_.sub_property_name}})", 
                        {% elif 'journal' in property_.value %}
                            display: "SUM({{property_.object_name}})",  
                        {% elif 'sum' in property_.value %}
                            display: "SUM({{property_.object_name}} - {{property_.property_name}})", 
                        {% else %}
                            display: "{% read_display_column_with_request property_.object_type related_property_column_display %} {{property_.object_type}}", 
                        {% endif %}
                            
                            value: "{{property_.value}}", group: "Related Data" 
                    {% endif %}
                        })
                    {% endfor %}
                {% endif %}
            {% endif %}

            //=========================================================================
            // Cases Related Formula
            {% if page_group_type == constant.TYPE_OBJECT_CASE %}
                {% if related_attendance_employee_properties or attendance_employee_associates_properties %}
                    {% for property_ in related_attendance_employee_properties %}
                        groupedSuggestions['Related Data'].push({ display: "{% if LANGUAGE_CODE == 'ja' %}従業員{% else %}Employee (Related){% endif %} - ({{property_.name}})", value: "{{property_.id}}|attendance|employee|related|sum", group: "Related Data" })
                    {% endfor %}
                    {% for property_ in attendance_employee_associates_properties %}
                        groupedSuggestions['Related Data'].push({ display: "{% if LANGUAGE_CODE == 'ja' %}従業員{% else %}Employee (Association){% endif %} - ({{property_.name}})", value: "{{property_.id}}|attendance|employee|associate|sum", group: "Related Data" })
                    {% endfor %}
                {% endif %}
                {% if related_task_properties %}
                    {% for property_ in related_task_properties %}
                        groupedSuggestions['Related Data'].push({ display: "{% if LANGUAGE_CODE == 'ja' %}タスク{% else %}Task{% endif %} - ({{property_.name}}) - SUM", value: "{{property_.id}}|tasks|default_association|sum", group: "Related Data" })
                    {% endfor %}
                {% endif %}
            {% endif %}
            
            {% if functions_properties %}
                groupedSuggestions['Functions']=[]
                {% if page_group_type == constant.TYPE_OBJECT_CASE %}
                    {% for function in functions_properties %}
                        {% if function == 'total_wage_cost' %}
                            groupedSuggestions['Functions'].push({ display: "{% if LANGUAGE_CODE == 'ja' %}総賃金費用{% else %}Total Wage Cost{% endif %}", value: "{{function}}|function", group: "Functions" })
                        {% endif %}
                    {% endfor %}
                {% endif %}
            {% endif %}

            //=========================================================================

            function createTag(tagData) {
                var tag = document.createElement('div');
                tag.classList.add('tag');
                tag.innerHTML = `
                    <span>${tagData.display}</span>
                    <span class="tag-remove">×</span>
                `;
                tag.dataset.value = tagData.value;
                tag.dataset.group = tagData.group;
                return tag;
            }

            function addTag(tagData) {
                if (tagData.display.trim() !== '') {
                    var tag = createTag(tagData);
                    tagsContainer.insertBefore(tag, tagInput);
                    tags.push(tagData);
                    tagInput.value = '';
                    updateHiddenInput();
                }
            }

            function removeTag(tagElement) {
                var index = Array.from(tagsContainer.children).indexOf(tagElement);
                if (index > -1) {
                    tags.splice(index, 1);
                    tagElement.remove();
                    updateHiddenInput();
                }
            }

            function updateHiddenInput() {
                hiddenInput.value = JSON.stringify(tags);
            }

            function showSuggestions() {
                suggestionsContainer.innerHTML = '';
                for (const [group, suggestions] of Object.entries(groupedSuggestions)) {
                    const groupElement = document.createElement('div');
                    groupElement.classList.add('suggestion-group');
                    
                    const groupTitle = document.createElement('div');
                    groupTitle.classList.add('group-title');
                    if (group == 'Operator'){
                        var translated_group = "{% if LANGUAGE_CODE == 'ja' %}演算子{% else %}Operator{% endif %}"
                    }
                    else if (group == 'Property - Number'){
                        var translated_group = "{% if LANGUAGE_CODE == 'ja' %}プロパティ - 数値{% else %}Property - Number{% endif %}"
                    }
                    else if (group == 'Property - Text'){
                        var translated_group = "{% if LANGUAGE_CODE == 'ja' %}プロパティ - テキスト{% else %}Property - Text{% endif %}"
                    }
                    else if (group == 'Property - Price Information'){
                        var translated_group = "{% if LANGUAGE_CODE == 'ja' %}プロパティ - 商品項目{% else %}Property - Line Items{% endif %}"
                    }
                    else if (group == 'Related Data'){
                        var translated_group = "{% if LANGUAGE_CODE == 'ja' %}アソシエーション{% else %}Associations{% endif %}"
                    }
                    else if (group == 'Custom Property - Number'){
                        var translated_group = "{% if LANGUAGE_CODE == 'ja' %}カスタムプロパティ - 数値{% else %}Custom Property - Number{% endif %}"
                    } 
                    {% if  functions_properties %}
                    else if (group == 'Functions'){
                        var translated_group = "{% if LANGUAGE_CODE == 'ja' %}関数{% else %}Functions{% endif %}"
                    }
                    {% endif %}
                    
                    
                    groupTitle.textContent = translated_group;
                    groupElement.appendChild(groupTitle);
            
                    suggestions.forEach(suggestion => {
                        const item = document.createElement('div');
                        item.classList.add('suggestion-item');
                        item.textContent = suggestion.display;
                        item.onclick = function() {
                            addTag(suggestion);
                        };
                        groupElement.appendChild(item);
                    });
            
                    suggestionsContainer.appendChild(groupElement);
                }
                suggestionsContainer.style.display = 'block';
            }

            function hideSuggestions() {
                suggestionsContainer.style.display = 'none';
            }

            function determineGroup(value) {
                for (var [group, suggestions] of Object.entries(groupedSuggestions)) {
                    if (suggestions.some(s => s.value === value || s.display === value)) {
                        return group;
                    }
                }
                if (!isNaN(value)) {
                    return 'Property';
                }
                return 'Other';
            }

            tagsContainer.addEventListener('click', function(e) {
                if (e.target.classList.contains('tag-remove')) {
                    removeTag(e.target.parentElement);
                }
            });
            

            tagInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    const inputValue = this.value.trim();
                    let tagData = { display: inputValue, value: inputValue, group: determineGroup(inputValue) };
                    
                    // Check if the input matches any suggestion
                    for (const suggestions of Object.values(groupedSuggestions)) {
                        const match = suggestions.find(s => s.display === inputValue || s.value === inputValue);
                        if (match) {
                            tagData = match;
                            break;
                        }
                    }
                    
                    addTag(tagData);
                    showSuggestions();
                    e.preventDefault()
                }
            });

            tagInput.addEventListener('input', function () {
                const inputValue = this.value.trim().toLowerCase();
                const suggestionsContainer = document.getElementById('suggestions');
                suggestionsContainer.innerHTML = ''; // clear previous

                if (!inputValue) {
                    suggestionsContainer.style.display = 'none';
                    return;
                }

                let matches = [];

                // Flatten and filter suggestions
                Object.values(groupedSuggestions).forEach(suggestions => {
                    matches = matches.concat(
                        suggestions.filter(s =>
                            s.display.toLowerCase().includes(inputValue) ||
                            s.value.toLowerCase().includes(inputValue)
                        )
                    );
                });

                if (matches.length === 0) {
                    suggestionsContainer.style.display = 'none';
                    return;
                }

                // Show suggestions
                matches.forEach(s => {
                    const div = document.createElement('div');
                    div.className = 'suggestion-item';
                    div.textContent = s.display;
                    div.addEventListener('mousedown', function () {
                        addTag(s);
                        tagInput.value = '';
                        suggestionsContainer.innerHTML = '';
                        suggestionsContainer.style.display = 'none';
                    });
                    suggestionsContainer.appendChild(div);
                });

                suggestionsContainer.style.display = 'block';
            });

            tagInput.addEventListener('blur', function () {
                setTimeout(() => {
                    document.getElementById('suggestions').style.display = 'none';
                }, 200); // Delay to allow click
            });

            tagInput.addEventListener('focus', showSuggestions);

            document.addEventListener('click', function(e) {
                if (!tagsContainer.contains(e.target)) {
                    hideSuggestions();
                }        
            });


            function customJSONParse(str) {
                // Remove the outer square brackets
                str = str.trim().slice(1, -1);
                // Split the string into individual objects
                const objects = str.split('}, {');
                
                return objects.map(obj => {
                    // Add back the curly braces
                    obj = obj.includes('{') ? obj : '{' + obj;
                    obj = obj.includes('}') ? obj : obj + '}';
                    
                    // Replace single quotes with double quotes
                    obj = obj.replace(/'/g, '"');
                    
                    // Parse the individual object
                    return JSON.parse(obj);
                });
            }


            function loadPredefinedTags(predefinedValue) {
                try {
                    
                    var predefinedTags = customJSONParse(predefinedValue);
                
                    //var predefinedTags = JSON.parse(predefinedValue);
                    // Clear existing tags
                    tags.length = 0; // Clear the array without reassigning
                    tagsContainer.querySelectorAll('.tag').forEach(tag => tag.remove());
                    // Add predefined tags
                    predefinedTags.forEach(tagData => addTag(tagData));
                } catch (error) {
                    console.error("Error parsing predefined value:", error);
                }
            }

            {% if property.choice_value|safe|string_list_to_list %}
                var predefinedValue = "{{property.choice_value|safe|string_list_to_list|safe}}"
                loadPredefinedTags(predefinedValue);
            {% endif %}
    })
        
    </script>
{% elif type == 'text' %}

    {% comment %} Always show unique limit checkbox for text properties {% endcomment %}
    <div class="mb-6 form-check form-switch form-check-custom form-check-solid">
        <input id="text-unique-check" name="text-unique-check" class="form-check-input" type="checkbox" {% if text_unique_check %} checked {% endif %}>
        <label class="form-check-label fw-semibold text-gray-700 ms-3" for="text-unique-check">
            {% if LANGUAGE_CODE == 'ja' %}
            ユニーク制限
            {% else %}
            Unique Limit
            {% endif %}
        </label>
    </div>

    {% if property %} {% comment %} Text on custom property {% endcomment %}
        {% comment %} Additional property-specific content can go here {% endcomment %}
    {% else %}
        {% if max_text_length %}
            <div class="mb-10">
                <label class="{% include 'data/utility/form-label.html' %}">
                    <span class="">
                        {% if LANGUAGE_CODE == 'ja' %}
                        テキスト表示の文字数
                        {% else %}
                        Text Display Length
                        {% endif %}
                    </span>
                </label>


                <input
                {% if LANGUAGE_CODE == 'ja' %}
                placeholder="テキスト表示の文字数"
                {% else %}
                placeholder="Text Display Length"
                {% endif %}
                name="max_text_length" class="form-control" {% if max_text_length.max_text_length %} value="{{max_text_length.max_text_length}}" {% else %} value="25" {% endif %}
                type='number'
                ></input>
            </div>
        {% endif %}
    {% endif %}

    <div class="mb-6 form-check form-switch form-check-custom form-check-solid">
        <input id="text-required-field" name="required-field" class="form-check-input" type="checkbox" {% if property.required_field or custom_property_value.required_field %} checked {% endif %}>
        <label class="form-check-label fw-semibold text-gray-700 ms-3" for="text-required-field">
            {% if LANGUAGE_CODE == 'ja' %}
            必須フィールド
            {% else %}
            Required Field
            {% endif %}
        </label>
    </div>

{% elif type|in_list:'attendance,purchase_order,order_objects,case,production_objects'%}
    <div class="mb-6 form-check form-switch form-check-custom form-check-solid">
        <input id="multiple-select-choice" name="multiple-select-choice" class="form-check-input" type="checkbox" {% if property.multiple_select or multiple_select_choice %} checked {% endif %}>
        <label class="form-check-label fw-semibold text-gray-700 ms-3" for="multiple-select-choice">
            {% if LANGUAGE_CODE == 'ja' %}
            複数選択
            {% else %}
            Multiple Select
            {% endif %}
        </label>
    </div>
{% elif type == 'shipping_info' %}
    <div class="fv-rowd-flex flex-column">
        <div class="mt-6 mb-3 d-flex w-100">
            <label class="fs-5 fw-bold mb-2">
                <span class=""> 
                    {% if LANGUAGE_CODE == 'ja'%}
                    フィールド
                    {% else %}
                    Fields
                    {% endif %}
                </span>
            </label>
            
            <a  class="{% if page_group_type != 'conversation' %}{% if property.id != 'slip_type' and property.id != 'delivery_status' and property.id != 'settle_choice' and property.id != 'status' and property.id != 'production_status' %}{% if property.immutable %}d-none{% endif %}{% endif %}{% endif %}"
                href='javascript:;'
                hx-get="{% host_url 'property_child' host 'app' %}" 
                hx-vals = '{"type": "{{type}}", "page_group_type": "{{page_group_type}}", "add": "true"}'
                hx-swap="beforeend"
                hx-target="#choice-options"
                hx-trigger="click"
            >
                <span class="svg-icon svg-icon-primary svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <polygon points="0 0 24 0 24 24 0 24"/>
                        <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                        <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                    </g>
                </span>
                {% if LANGUAGE_CODE == 'ja'%}
                フィールドを追加
                {% else %}
                Add Field
                {% endif %}

            </a>

        </div>

    </div>

    <div id="response-div"></div>

    <div id="choice-options">
        {% for field in property.sub_property|string_list_to_list %}
            {% include 'data/property/property-field-option.html' with field=field property=property %}
        {% empty %}
            {% include 'data/property/property-field-option.html' %}
        {% endfor %}
    </div>

    <script>
        var myElement = document.getElementById("choice-options");
        new Sortable(myElement, {
            animation: 150,
            ghostClass: 'blue-background-class',
            handle : '.task-grip'
        });

        function onChangeChoiceOption(inputElement) {
            const inputName = inputElement.name;
            const choiceValueElements = document.querySelectorAll(`input[name="${inputName}"]`);
            const choiceValues = Array.from(choiceValueElements).map(el => el.value);
        
            const currentChoiceValue = inputElement.value;
            inputElement.value = currentChoiceValue.replaceAll(';','')
            const warningLabel = inputElement.parentElement.nextElementSibling;
        
            if (choiceValues.filter(value => value === currentChoiceValue).length > 1) {
                {% if LANGUAGE_CODE == 'ja' %}
                    warningLabel.textContent = `オプション "${currentChoiceValue}" は既に存在しています。`;
                {% else %}
                    warningLabel.textContent = `Option "${currentChoiceValue}" already exists.`;
                {% endif %}
        
                warningLabel.style.display = 'block';
                inputElement.style.border = '1px solid red'; 
                
                // Prevent form submission
                inputElement.form.onsubmit = function(event) {
                    event.preventDefault();
                };
            } else {
                warningLabel.textContent = '';
                warningLabel.style.display = 'none';
                inputElement.style.border = ''; 
                
                // Allow form submission
                inputElement.form.onsubmit = null;
            }
        }

    </script>

{% elif type == 'property_sync' %}
    <div class="fv-rowd-flex flex-column">
        <label class="fs-5 fw-bold mb-2">
            <span class=""> 
                {% if LANGUAGE_CODE == 'ja'%}
                同期元のプロパティ
                {% else %}
                Property to Sync From
                {% endif %}
            </span>
        </label>

        <select
            class="h-40px form-select form-select-solid border bg-white select2-this mb-5"
            data-placeholder="{% if LANGUAGE_CODE == 'ja'%}ソースプロパティの選択{% else %}Select Source Property{% endif %}"
            name="source_property"
            onchange="validateProperties()"
            required>
            <option value="default">{% if LANGUAGE_CODE == 'ja'%}デフォルト（このプロパティを使用）{% else %}Default (use this property){% endif %}</option>
            {% for property_ in default_properties %}
                {% if page_group_type == 'commerce_items' %}
                    <option value="{{ property_ }}" {% if property_ == property.source_property %}selected{% endif %}>{{ property_|display_column_items:request }}</option>
                {% elif page_group_type == 'commerce_orders' %}
                    <option value="{{ property_ }}" {% if property_ == property.source_property %}selected{% endif %}>{{ property_|display_column_orders:request }}</option>
                {% else %}
                    <option value="{{ property_ }}" {% if property_ == property.source_property %}selected{% endif %}>{{ property_ }}</option>
                {% endif %}

            {% endfor %}
        </select>

        <label class="fs-5 fw-bold mb-2">
            <span class=""> 
                {% if LANGUAGE_CODE == 'ja'%}
                同期先のプロパティ
                {% else %}
                Property to Sync To
                {% endif %}
            </span>
        </label>
        
        <select
            class="h-40px form-select form-select-solid border bg-white select2-this"
            data-placeholder="{% if LANGUAGE_CODE == 'ja'%}同期するプロパティを選択{% else %}Select Property to Sync{% endif %}"
            name="pair_property"
            onchange="validateProperties()"
            required>
            <option value="default">{% if LANGUAGE_CODE == 'ja'%}デフォルト（このプロパティを使用）{% else %}Default (use this property){% endif %}</option>
            {% for property_ in custom_properties %}
                <option value="{{ property_.id }}" {% if property_.id|stringformat:"s" == property.pair_property %}selected{% endif %}>{{ property_.name }}</option>
            {% endfor %}
        </select>

        <div style="color: red; margin-top: 10px;" id="warning-text" >
            {% if LANGUAGE_CODE == "ja" %}
                同期元と同期先のプロパティが同じです。
            {% else %}
                Source and Output properties cannot be the same.
            {% endif %}
        </div>

        <script>

            function validateProperties() {
                const sourcePropertySelect = document.querySelector('select[name="source_property"]');
                const pairPropertySelect = document.querySelector('select[name="pair_property"]');
                const sourceValue = sourcePropertySelect.value;
                const pairValue = pairPropertySelect.value;
                const propertyCreateButton = document.getElementById('property_create');
                const propertyUpdateButton = document.getElementById('property_update');
                const warningText = document.getElementById('warning-text');
                const buttonToDisable = propertyCreateButton || propertyUpdateButton;

                if (sourceValue === pairValue) {
                    warningText.classList.remove('d-none');
                    if (buttonToDisable) {
                        buttonToDisable.disabled = true;
                    }
                } else {
                    warningText.classList.add('d-none');
                    if (buttonToDisable) {
                        buttonToDisable.disabled = false;
                    }
                }
            }
                    
            $(document).ready(function() {
                validateProperties();
            });
        </script>

    </div>

    <div class="mt-6 mb-3 d-flex w-100">
        <label class="fs-5 fw-bold mb-2">
            <span class=""> 
                {% if LANGUAGE_CODE == 'ja'%}
                フィールド
                {% else %}
                Fields
                {% endif %}
            </span>
        </label>
        
        <a  class="{% if page_group_type != 'conversation' %}{% if property.id != 'slip_type' and property.id != 'delivery_status' and property.id != 'settle_choice' and property.id != 'status' and property.id != 'production_status' %}{% if property.immutable %}d-none{% endif %}{% endif %}{% endif %}"
            href='javascript:;'
            hx-get="{% host_url 'property_child' host 'app' %}" 
            hx-vals = '{"type": "{{type}}", "page_group_type": "{{page_group_type}}", "add": "true"}'
            hx-swap="beforeend"
            hx-target="#choice-options"
            hx-trigger="click"
        >
            <span class="svg-icon svg-icon-primary svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <polygon points="0 0 24 0 24 24 0 24"/>
                    <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                    <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                </g>
            </span>
            {% if LANGUAGE_CODE == 'ja'%}
            フィールドを追加
            {% else %}
            Add Field
            {% endif %}

        </a>

    </div>

    <div id="response-div"></div>

    <div id="choice-options">
        {% for field in property.sync_conditions|string_list_to_list %}
            {% include 'data/property/property-sync-option.html' with field=field property=property %}
        {% empty %}
            {% include 'data/property/property-sync-option.html' %}
        {% endfor %}
    </div>

    <script>
        var myElement = document.getElementById("choice-options");
        new Sortable(myElement, {
            animation: 150,
            ghostClass: 'blue-background-class',
            handle : '.task-grip'
        });

        function onChangeChoiceOption(inputElement) {
            const inputName = inputElement.name;
            const choiceValueElements = document.querySelectorAll(`input[name="${inputName}"]`);
            const choiceValues = Array.from(choiceValueElements).map(el => el.value);
        
            const currentChoiceValue = inputElement.value;
            inputElement.value = currentChoiceValue.replaceAll(';','')
            const warningLabel = inputElement.parentElement.nextElementSibling;
        
            if (choiceValues.filter(value => value === currentChoiceValue).length > 1) {
                {% if LANGUAGE_CODE == 'ja' %}
                    warningLabel.textContent = `オプション "${currentChoiceValue}" は既に存在しています。`;
                {% else %}
                    warningLabel.textContent = `Option "${currentChoiceValue}" already exists.`;
                {% endif %}
        
                warningLabel.style.display = 'block';
                inputElement.style.border = '1px solid red'; 
                
                // Prevent form submission
                inputElement.form.onsubmit = function(event) {
                    event.preventDefault();
                };
            } else {
                warningLabel.textContent = '';
                warningLabel.style.display = 'none';
                inputElement.style.border = ''; 
                
                // Allow form submission
                inputElement.form.onsubmit = null;
            }
        }

    </script>

{% elif type == 'language' %}
    <div class="fv-rowd-flex flex-column mb-5">
        <label class="fs-5 fw-bold mb-2">
            <span class=""> 
                {% if LANGUAGE_CODE == 'ja'%}
                同期元のプロパティ
                {% else %}
                Property to Sync From
                {% endif %}
            </span>
        </label>

        <select
            class="h-40px form-select form-select-solid border bg-white select2-this mb-5"
            data-placeholder="{% if LANGUAGE_CODE == 'ja'%}ソースプロパティの選択{% else %}Select Source Property{% endif %}"
            name="source_property"
            onchange="validateProperties()"
            required>
            <option value="default">{% if LANGUAGE_CODE == 'ja'%}デフォルト（このプロパティを使用）{% else %}Default (use this property){% endif %}</option>
            {% for property_ in default_properties %}
                {% if page_group_type == 'commerce_items' %}
                    <option value="{{ property_ }}" {% if property_ == property.source_property %}selected{% endif %}>{{ property_|display_column_items:request }}</option>
                {% elif page_group_type == 'commerce_orders' %}
                    <option value="{{ property_ }}" {% if property_ == property.source_property %}selected{% endif %}>{{ property_|display_column_orders:request }}</option>
                {% else %}
                    <option value="{{ property_ }}" {% if property_ == property.source_property %}selected{% endif %}>{{ property_ }}</option>
                {% endif %}

            {% endfor %}
        </select>

        <label class="fs-5 fw-bold mb-2">
            <span class=""> 
                {% if LANGUAGE_CODE == 'ja'%}
                同期先のプロパティ
                {% else %}
                Property to Sync To
                {% endif %}
            </span>
        </label>
        
        <select
            class="h-40px form-select form-select-solid border bg-white select2-this"
            data-placeholder="{% if LANGUAGE_CODE == 'ja'%}同期するプロパティを選択{% else %}Select Property to Sync{% endif %}"
            name="pair_property"
            onchange="validateProperties()"
            required>
            <option value="default">{% if LANGUAGE_CODE == 'ja'%}デフォルト（このプロパティを使用）{% else %}Default (use this property){% endif %}</option>
            {% for property_ in custom_properties %}
                <option value="{{ property_.id }}" {% if property_.id|stringformat:"s" == property.pair_property %}selected{% endif %}>{{ property_.name }}</option>
            {% endfor %}
        </select>

        <div style="color: red; margin-top: 10px; margin-bottom:10px;" id="warning-text" >
            {% if LANGUAGE_CODE == "ja" %}
                同期元と同期先のプロパティが同じです。
            {% else %}
                Source and Output properties cannot be the same.
            {% endif %}
        </div>

        <label class="fs-5 fw-bold mb-2 mt-3">
            <span>
                {% if LANGUAGE_CODE == 'ja' %}
                    同期元言語
                {% else %}
                    Source Language
                {% endif %}
            </span>
        </label>

        <select class="h-40px form-select form-select-solid border bg-white select2-this"
            data-placeholder="{% if LANGUAGE_CODE == 'ja' %}言語を選択{% else %}Select Language{% endif %}"
            name="source_language"
            required>
            {% for code, language, language_ja in languages %}
                {% with sync_condition=property.sync_conditions|string_list_to_list %}
                    <option value="{{ code }}" {% if code == sync_condition.0 %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja' %}{{language_ja}}{% else %}{{language}}{% endif %}</option>
                {% endwith %}
            {% endfor %}
        </select>

        <label class="fs-5 fw-bold mb-2 mt-3">
            <span>
                {% if LANGUAGE_CODE == 'ja' %}
                    同期先言語
                {% else %}
                    Output Language
                {% endif %}
            </span>
        </label>

        <select class="h-40px form-select form-select-solid border bg-white select2-this"
            data-placeholder="{% if LANGUAGE_CODE == 'ja' %}言語を選択{% else %}Select Language{% endif %}"
            name="pair_language"
            required>
            {% for code, language, language_ja in languages %}
                {% with sync_condition=property.sync_conditions|string_list_to_list %}
                    <option value="{{ code }}" {% if code == sync_condition.1 %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja' %}{{language_ja}}{% else %}{{language}}{% endif %}</option>
                {% endwith %}
            {% endfor %}
        </select>


        <script>

            function validateProperties() {
                const sourcePropertySelect = document.querySelector('select[name="source_property"]');
                const pairPropertySelect = document.querySelector('select[name="pair_property"]');
                const sourceValue = sourcePropertySelect.value;
                const pairValue = pairPropertySelect.value;
                const propertyCreateButton = document.getElementById('property_create');
                const propertyUpdateButton = document.getElementById('property_update');
                const warningText = document.getElementById('warning-text');
                const buttonToDisable = propertyCreateButton || propertyUpdateButton;

                if (sourceValue === pairValue) {
                    warningText.classList.remove('d-none');
                    if (buttonToDisable) {
                        buttonToDisable.disabled = true;
                    }
                } else {
                    warningText.classList.add('d-none');
                    if (buttonToDisable) {
                        buttonToDisable.disabled = false;
                    }
                }
            }
                    
            $(document).ready(function() {
                validateProperties();
            });
        </script>

    </div>

    <script>

        function onChangeChoiceOption(inputElement) {
            const inputName = inputElement.name;
            const choiceValueElements = document.querySelectorAll(`input[name="${inputName}"]`);
            const choiceValues = Array.from(choiceValueElements).map(el => el.value);
        
            const currentChoiceValue = inputElement.value;
            inputElement.value = currentChoiceValue.replaceAll(';','')
            const warningLabel = inputElement.parentElement.nextElementSibling;
        
            if (choiceValues.filter(value => value === currentChoiceValue).length > 1) {
                {% if LANGUAGE_CODE == 'ja' %}
                    warningLabel.textContent = `オプション "${currentChoiceValue}" は既に存在しています。`;
                {% else %}
                    warningLabel.textContent = `Option "${currentChoiceValue}" already exists.`;
                {% endif %}
        
                warningLabel.style.display = 'block';
                inputElement.style.border = '1px solid red'; 
                
                // Prevent form submission
                inputElement.form.onsubmit = function(event) {
                    event.preventDefault();
                };
            } else {
                warningLabel.textContent = '';
                warningLabel.style.display = 'none';
                inputElement.style.border = ''; 
                
                // Allow form submission
                inputElement.form.onsubmit = null;
            }
        }

    </script>

{% elif type == 'currency' %}
    <div class="fv-rowd-flex flex-column mb-5">
        <label class="fs-5 fw-bold mb-2">
            <span class=""> 
                {% if LANGUAGE_CODE == 'ja'%}
                同期元のプロパティ
                {% else %}
                Property to Sync From
                {% endif %}
            </span>
        </label>

        <select
            class="h-40px form-select form-select-solid border bg-white select2-this mb-5"
            data-placeholder="{% if LANGUAGE_CODE == 'ja'%}ソースプロパティの選択{% else %}Select Source Property{% endif %}"
            name="source_property"
            onchange="validateProperties()"
            required>
            <option value="default">{% if LANGUAGE_CODE == 'ja'%}デフォルト（このプロパティを使用）{% else %}Default (use this property){% endif %}</option>
            {% for property_ in default_properties %}
                {% if page_group_type == 'commerce_items' %}
                    <option value="{{ property_ }}" {% if property_ == property.source_property %}selected{% endif %}>{{ property_|display_column_items:request }}</option>
                {% elif page_group_type == 'commerce_orders' %}
                    <option value="{{ property_ }}" {% if property_ == property.source_property %}selected{% endif %}>{{ property_|display_column_orders:request }}</option>
                {% else %}
                    <option value="{{ property_ }}" {% if property_ == property.source_property %}selected{% endif %}>{{ property_ }}</option>
                {% endif %}

            {% endfor %}
        </select>

        <label class="fs-5 fw-bold mb-2">
            <span class=""> 
                {% if LANGUAGE_CODE == 'ja'%}
                同期先のプロパティ
                {% else %}
                Property to Sync To
                {% endif %}
            </span>
        </label>
        
        <select
            class="h-40px form-select form-select-solid border bg-white select2-this"
            data-placeholder="{% if LANGUAGE_CODE == 'ja'%}同期するプロパティを選択{% else %}Select Property to Sync{% endif %}"
            name="pair_property"
            onchange="validateProperties()"
            required>
            <option value="default">{% if LANGUAGE_CODE == 'ja'%}デフォルト（このプロパティを使用）{% else %}Default (use this property){% endif %}</option>
            {% for property_ in custom_properties %}
                <option value="{{ property_.id }}" {% if property_.id|stringformat:"s" == property.pair_property %}selected{% endif %}>{{ property_.name }}</option>
            {% endfor %}
        </select>

        <div style="color: red; margin-top: 10px; margin-bottom:10px;" id="warning-text" >
            {% if LANGUAGE_CODE == "ja" %}
                同期元と同期先のプロパティが同じです。
            {% else %}
                Source and Output properties cannot be the same.
            {% endif %}
        </div>

        <label class="fs-5 fw-bold mb-2 mt-3">
            <span>
                {% if LANGUAGE_CODE == 'ja' %}
                    通貨
                {% else %}
                    Currency
                {% endif %}
            </span>
        </label>

        <select class="h-40px form-select form-select-solid border bg-white select2-this"
            data-placeholder="{% if LANGUAGE_CODE == 'ja' %}通貨の選択{% else %}Select Currency{% endif %}"
            name="selected_currency"
            required>
            {% for code, display, symbol in currencies %}
                <option value="{{code}}" {% if code == property.sync_conditions %}selected{% endif %}>{{display}}</option>
            {% endfor %}
        </select>

        <script>

            function validateProperties() {
                const sourcePropertySelect = document.querySelector('select[name="source_property"]');
                const pairPropertySelect = document.querySelector('select[name="pair_property"]');
                const sourceValue = sourcePropertySelect.value;
                const pairValue = pairPropertySelect.value;
                const propertyCreateButton = document.getElementById('property_create');
                const propertyUpdateButton = document.getElementById('property_update');
                const warningText = document.getElementById('warning-text');
                const buttonToDisable = propertyCreateButton || propertyUpdateButton;

                if (sourceValue === pairValue) {
                    warningText.classList.remove('d-none');
                    if (buttonToDisable) {
                        buttonToDisable.disabled = true;
                    }
                } else {
                    warningText.classList.add('d-none');
                    if (buttonToDisable) {
                        buttonToDisable.disabled = false;
                    }
                }
            }
                    
            $(document).ready(function() {
                validateProperties();
            });
        </script>

    </div>

    <script>

        function onChangeChoiceOption(inputElement) {
            const inputName = inputElement.name;
            const choiceValueElements = document.querySelectorAll(`input[name="${inputName}"]`);
            const choiceValues = Array.from(choiceValueElements).map(el => el.value);
        
            const currentChoiceValue = inputElement.value;
            inputElement.value = currentChoiceValue.replaceAll(';','')
            const warningLabel = inputElement.parentElement.nextElementSibling;
        
            if (choiceValues.filter(value => value === currentChoiceValue).length > 1) {
                {% if LANGUAGE_CODE == 'ja' %}
                    warningLabel.textContent = `オプション "${currentChoiceValue}" は既に存在しています。`;
                {% else %}
                    warningLabel.textContent = `Option "${currentChoiceValue}" already exists.`;
                {% endif %}
        
                warningLabel.style.display = 'block';
                inputElement.style.border = '1px solid red'; 
                
                // Prevent form submission
                inputElement.form.onsubmit = function(event) {
                    event.preventDefault();
                };
            } else {
                warningLabel.textContent = '';
                warningLabel.style.display = 'none';
                inputElement.style.border = ''; 
                
                // Allow form submission
                inputElement.form.onsubmit = null;
            }
        }

    </script>

{% elif type == 'task' %}
    <div class="mb-3">
        <label class="fs-5 fw-bold mb-2">
            <span class=""> 
                {% if LANGUAGE_CODE == 'ja'%}
                プロジェクトID
                {% else %}
                Project ID
                {% endif %}
            </span>
        </label>

        <select
            class="h-40px form-select form-select-solid border bg-white select2-this-lazy-task-cf"
            data-placeholder="{% if LANGUAGE_CODE == 'ja'%}プロジェクトを選択{% else %}Select Project{% endif %}"
            name="project"
            required>
            {% if property.project_target%}
                <option value="{{ property.project_target.id }}" selected >
                    {{property.project_target.title}}
                </option>
            {% endif %}
        </select>
    </div>

    <script>
        $(document).ready(function() {
            $('.select2-this-lazy-task-cf').select2({
                ajax: {
                    delay: 250, // wait 250 milliseconds before triggering the request
                    dataType: 'json',
                    url: '{% host_url "projects_options" host "app" %}',
                    data: function (params) {
                            var query = {
                                q: params.term,
                                page: params.page || 1,
                                json_response: true
                            }
                            return query;
                        },
                    minimumInputLength: 2,
                },
                language: {
                    "noResults": function(){
                        return "{% if LANGUAGE_CODE == 'ja' %}プロジェクトが見つかりません{% else %}No Project is found{% endif %}";
                    },
                    searching: function(){
                        return "{% if LANGUAGE_CODE == 'ja'%}検索中{% else %}Searching{% endif %}...";
                    },
                    "loadingMore": function(){
                        return "{% if LANGUAGE_CODE == 'ja'%}読み込み中{% else %}Loading more{% endif %}...";
                    },
                },
            });
        });
    </script>

{% elif type == 'price-information' %}
    {% if property %}
        <div class="d-flex justify-content-start align-items-center mb-3">
            <label class="fs-5 fw-bolder text-active-primary ms-0 py-0 border-0 me-3">
                {% if LANGUAGE_CODE == 'ja'%}
                商品項目フィールド
                {% else %}
                Line Item Fields
                {% endif %}
            </label>
            <a id="new-property" class="py-1 d-flex align-items-center"
                hx-get="{% host_url 'new_property' host 'app' %}" 
                hx-vals='{"page_group_type": "{{page_group_type}}", "line_item_property" : "true", "set_id": "{{form.id}}", "line_item_custom_property": "{{property.id}}"}'
                hx-target="#view-settings-drawer"
                hx-on="htmx:beforeSend: 
                    document.getElementById('expenses_form_lg').innerHTML = '';
                    "
                hx-indicator=".loading-drawer-spinner,.expenses-form"
                hx-trigger="click"
                type="button">
                <span class="svg-icon svg-icon-primary svg-icon-2x">
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <polygon points="0 0 24 0 24 24 0 24"/>
                            <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                            <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                        </g>
                    </svg>
                </span>
                <span class="fs-7 ps-1 fw-bolder w-100px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    新規 プロパティ
                    {% else %}
                    New Property
                    {% endif %}
                </span>
            </a>
        </div>
        <div id="line-item-property-custom-table-container" class="mt-5 mb-3">
            <table id="line-item-property-custom-table" class="table table-row-dashed align-middle fs-6 gy-2 my-0 table-content">
                <thead class="{% include "data/utility/table-header.html" %}">
                    <tr class="align-middle">  

                        <th>
                            {% if LANGUAGE_CODE == 'ja' %}プロパティ名{% else %}Property Name{% endif %}
                        </th>

                        <th>
                            {% if LANGUAGE_CODE == 'ja' %}タイプ{% else %}Type{% endif %}
                        </th>

                        <th>
                            {% if LANGUAGE_CODE == 'ja' %}隠す{% else %}Hide{% endif %}
                        </th>

                    </tr>
                </thead>

                <tbody>
                    <tr>
                        <td class="fw-bolder min-w-100px">
                            {% if LANGUAGE_CODE == 'ja' %}商品{% else %}Item{% endif %}
                        </td>

                        <td class="fw-bold">
                            {% if LANGUAGE_CODE == 'ja' %}デフォルトのプロパティ{% else %}Default Property{% endif %}
                        </td>
                        
                        <td class="fw-bold">
                            <div class="form-check form-switch form-check-custom form-check-solid">
                                <input class="form-check-input" type="checkbox" id="property-check" name="property-check" value="item" {% if 'item' in property.hidden_properties|string_list_to_list %} checked {% endif %}>
                            </div>
                        </td>

                    </tr>

                    <tr>
                        <td class="fw-bolder min-w-100px">
                            {% if LANGUAGE_CODE == 'ja' %}商品価格{% else %}Item Price{% endif %}
                        </td>

                        <td class="fw-bold">
                            {% if LANGUAGE_CODE == 'ja' %}デフォルトのプロパティ{% else %}Default Property{% endif %}
                        </td>

                        <td class="fw-bold">
                            <div class="form-check form-switch form-check-custom form-check-solid">
                                <input class="form-check-input" type="checkbox" id="property-check" name="property-check" value="item_price" {% if 'item_price' in property.hidden_properties|string_list_to_list %} checked {% endif %}>
                            </div>
                        </td>
                    </tr>

                    <tr class="cell-border-bottom" style="border-bottom: 2px solid rgb(221, 221, 221);">
                        <td class="fw-bolder min-w-100px">
                            {% if LANGUAGE_CODE == 'ja' %}商品数{% else %}Item Amount{% endif %}
                        </td>

                        <td class="fw-bold">
                            {% if LANGUAGE_CODE == 'ja' %}デフォルトのプロパティ{% else %}Default Property{% endif %}
                        </td>

                        <td class="fw-bold">
                            <div class="form-check form-switch form-check-custom form-check-solid">
                                <input class="form-check-input" type="checkbox" id="property-check" name="property-check" value="number_of_items" {% if 'number_of_items' in property.hidden_properties|string_list_to_list %} checked {% endif %}>
                            </div>
                        </td>
                    </tr>
                    
                        {% for item_prop_ in property.line_item_properties|string_list_to_list %}
                            <tr>
                                <td class="fw-bolder min-w-100px">
                                    <a class="text-dark text-hover-primary cursor-pointer fw-bolder"          
                                        hx-get="{% host_url 'manage_property' property.id host 'app' %}" 
                                        hx-vals='{"page_group_type": "{{page_group_type}}","set_id": "{{form.id}}","manage-type": "manage-line-item-custom-property","line_item_custom_property_type":"{{item_prop_.type}}", "line_item_custom_property": "{{item_prop_.name}}"}'
                                        hx-target="#view-settings-drawer"   
                                        hx-on="htmx:beforeSend: 
                                        document.getElementById('expenses_form_lg').innerHTML = '';
                                        "
                                        hx-indicator=".loading-drawer-spinner,.expenses-form"
                                        hx-trigger="click">
                                        {{item_prop_.name}}
                                    </a>
                                </td>
                            
                                <td class="fw-bold">
                                    {{item_prop_.type|display_property_type:LANGUAGE_CODE}}
                                </td>

                                <td class="fw-bold">
                                    <div class="form-check form-switch form-check-custom form-check-solid">
                                        <input class="form-check-input" type="checkbox" id="property-check" name="property-check" value="{{item_prop_.name}}" {% if item_prop_.name in property.hidden_properties|string_list_to_list %} checked {% endif %}>
                                    </div>
                                </td>
                            
                            </tr>


                        {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}
{% elif type == 'production_line' %}
    <link rel="stylesheet" href="{% static 'taskflow-components.css' %}">
    <div class="d-flex justify-content-start align-items-center mb-3">
        <label class="fs-5 fw-bolder text-active-primary ms-0 py-0 border-0 me-3">
            {% if LANGUAGE_CODE == 'ja'%}
            生産ラインフィールド
            {% else %}
            Production Line Fields
            {% endif %}
        </label>

        <a id="new-property" class="py-1 d-flex align-items-center new-production-line-btn"
            hx-get="{% host_url 'add_sub_property' host 'app' %}" 
            hx-vals='js:{"type": "{{type}}", "page_group_type": "{{page_group_type}}", "add": "new" , "property_id": "{{property.id}}"}'
            hx-target=".production_line_body"
            hx-trigger='click{% if not property %},load{% endif %}'
            hx-swap="beforeend"
            type="button">
            <span class="svg-icon svg-icon-primary svg-icon-2x">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <polygon points="0 0 24 0 24 24 0 24"/>
                        <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                        <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                    </g>
                </svg>
            </span>
            <span class="fs-7 ps-1 fw-bolder w-100">
                {% if LANGUAGE_CODE == 'ja'%}
                生産ラインを追加
                {% else %}
                Add Product Line
                {% endif %}
            </span>
        </a>
    </div>
    <div id="line-item-property-custom-table-container" class="mt-5 mb-3">
        <div id="line-item-property-custom-table" class="">
            <div class="table-like-header">
                <div class="table-like-row">  
                    <div class="table-like-cell header-cell">
                        {% if LANGUAGE_CODE == 'ja' %}プロパティ名{% else %}Property Name{% endif %}
                    </div>
                    <div class="table-like-cell header-cell">
                        {% if LANGUAGE_CODE == 'ja' %}価値{% else %}Value{% endif %}
                    </div>
                    <div class="table-like-cell header-cell w-10px"></div>
                </div>
            </div>

            <div class="production_line_body table-like-body">
                {% if property.choice_value and property.choice_value|is_dict %}
                    {% with vals_=property.choice_value|to_list%}
                        {% for line_id, vals in vals_.items %}
                            <div class='d-none'
                                hx-get="{% host_url 'add_sub_property' host 'app' %}" 
                                hx-vals='js:{"type": "{{type}}", "page_group_type": "{{page_group_type}}", "add": "{{vals}}", "line_id": "{{line_id}}" }'
                                hx-target="this"
                                hx-trigger='load'
                                hx-swap="outerHTML"></div>
                        {% endfor %}
                    {% endwith %}
                {% else %}
                    <script>
                        document.querySelector('.new-production-line-btn').click();
                    </script>
                {% endif %}
            </div>
        </div>
        
    </div>
    
    <script>
        // Initialize sortable for production line table
        var productionLineTable = document.querySelector('.production_line_body');
        if (productionLineTable) {
            new Sortable(productionLineTable, {
                animation: 150,
                ghostClass: 'blue-background-class',
                handle: '.production-line-grip',
                draggable: '.production_line_section',
                onEnd: function(evt) {
                    // Optional: Add callback for when sorting is complete
                    console.log('Production line reordered');
                }
            });
        }

        function delete_row_item(elm){
                var class_row = elm.parentElement.parentElement.parentElement;
                class_row.remove();
                var production_line_section = document.querySelectorAll('.production_line_section');
                if (production_line_section.length === 0) {
                    document.querySelector('.new-production-line-btn').click();
                }
            }
    </script>
    
    <style>
        .table-like-container {
            border: 1px solid #e4e6ef;
            border-radius: 0.475rem;
            overflow: hidden;
        }
        
        .table-like-header {
            background-color: #f9f9f9;
            border-bottom: 1px solid #e4e6ef;
        }
        
        .table-like-row {
            display: flex;
            align-items: center;
            border-bottom: 1px solid #e4e6ef;
            transition: background-color 0.2s ease;
        }
        
        .table-like-row:last-child {
            border-bottom: none;
        }
        
        .table-like-row:hover {
            background-color: #f5f8fa;
        }
        
        .table-like-cell {
            flex: 1;
            padding: 12px 16px;
            font-size: 0.875rem;
            line-height: 1.5;
        }
        
        .table-like-cell.w-10px {
            flex: 0 0 40px;
            width: 40px;
        }
        
        .header-cell {
            font-weight: 600;
            color: #3f4254;
        }
        
        .production-line-grip {
            opacity: 0.3;
            transition: opacity 0.2s ease;
        }
        
        .table-like-row:hover .production-line-grip {
            opacity: 1;
        }
        
        .blue-background-class {
            background-color: #e3f2fd !important;
            opacity: 0.8;
        }
        
        .cursor-grab {
            cursor: grab;
        }
        
        .cursor-grab:active {
            cursor: grabbing;
        }
    </style>
{% else %}
    <div class="mb-6 form-check form-switch form-check-custom form-check-solid">
        <input id="default-required-field" name="required-field" class="form-check-input" type="checkbox" {% if property.required_field or custom_property_value.required_field %} checked {% endif %}>
        <label class="form-check-label fw-semibold text-gray-700 ms-3" for="default-required-field">
            {% if LANGUAGE_CODE == 'ja' %}
            必須フィールド
            {% else %}
            Required Field
            {% endif %}
        </label>
    </div>
{% endif %}

<script>
    $('.select2-this').select2();
</script>

<style>
    .tagify__dropdown__item.Group {
        display: block;
        font-weight:boldest;
        color: #030303;
        align-items: center;
        padding: 8px 12px;
        background-color: #e8f5e9; 
        margin: 5px 0;
        cursor: pointer;
    }
</style>
