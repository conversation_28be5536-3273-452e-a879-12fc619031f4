{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

{% if property.id == 'counter_category' and not choice.value %}
    <div class="w-100 justify-content-center align-items-center d-flex mb-2">
            {% if LANGUAGE_CODE == 'ja'%}
                最初にいくつかのカテゴリを追加するか、「カテゴリ設定」の内側にカテゴリを保存します
            {% else %}
                Add some categories first, or save your category inside of "Category Setting"
            {% endif %}
</div>
{% endif %}

<div class="mb-5 {% if property.id == 'counter_category' and not choice.value or key and key != choice.refered_choice %}d-none{% endif %} sub-section">
    <div class="w-100 d-flex justify-content-center align-items-center cursor-grab z-index-1 position-relative task-grip">
        {% if page_group_type == 'conversation' or not property.immutable or property.id|in_list:'tax_rate,slip_type,delivery_status,settle_choice,status,job_type,case_status' %}
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-grip-vertical" viewBox="0 0 16 16">
            <path d="M3,2 C2.44771525,2 2,1.55228475 2,1 C2,0.44771525 2.44771525,0 3,0 C3.55228475,0 4,0.44771525 4,1 C4,1.55228475 3.55228475,2 3,2 Z M3,6 C2.44771525,6 2,5.55228475 2,5 C2,4.44771525 2.44771525,4 3,4 C3.55228475,4 4,4.44771525 4,5 C4,5.55228475 3.55228475,6 3,6 Z M3,10 C2.44771525,10 2,9.55228475 2,9 C2,8.44771525 2.44771525,8 3,8 C3.55228475,8 4,8.44771525 4,9 C4,9.55228475 3.55228475,10 3,10 Z M7,2 C6.44771525,2 6,1.55228475 6,1 C6,0.44771525 6.44771525,0 7,0 C7.55228475,0 8,0.44771525 8,1 C8,1.55228475 7.55228475,2 7,2 Z M7,6 C6.44771525,6 6,5.55228475 6,5 C6,4.44771525 6.44771525,4 7,4 C7.55228475,4 8,4.44771525 8,5 C8,5.55228475 7.55228475,6 7,6 Z M7,10 C6.44771525,10 6,9.55228475 6,9 C6,8.44771525 6.44771525,8 7,8 C7.55228475,8 8,8.44771525 8,9 C8,9.55228475 7.55228475,10 7,10 Z">
            </path>
        </svg>
        {% endif%}

        {% if property.id == 'counter_category' %}
        <input {% if choice %}readonly{% endif%} required class="form-control d-flex h-100 input-prehead {% if property.id == 'counter_category' %}w-100{% else %}w-50{% endif %}" {% if property.id != 'counter_category' %}name="choice_value"{% endif %}
            {% if LANGUAGE_CODE == 'ja'%}
            placeholder="値" 
            {% else %}
            placeholder="Value" 
            {% endif %}
            {% if property.id == 'counter_category' %}
            value = '{{choice.value|truncatechars:28}}'
            {% else %}
            value = '{{choice.value}}'
            {% endif %}
            oninput="onChangeChoiceOption(this);"
        />
        <select required class="form-select d-flex h-100 rounded-start-0 rounded-end-0 select2-this" id="choice_label_income_{{forloop.counter}}"
            {% if LANGUAGE_CODE == 'ja'%}
            placeholder="所得" 
            {% else %}
            placeholder="Income" 
            {% endif %}
            oninput="onUpdateCorresponding('{{forloop.counter}}');"
            {% if choice.label_income %}
                value="{{choice.label_income}}"
            {% elif forloop.first %}
                {% for category in related_custom_property_choices %}
                    {% if forloop.first %}value="{{category.value}}"{% endif %}
                {% endfor %}
            {% endif %}
            
        >
            {% for category in related_custom_property_choices %}
                <option {% if choice.label_income == category.value %}selected{% endif %} value="{{category.value}}">{{category.label|truncatechars:28}}</option>
            {% endfor %}
        </select>

        <select required class="form-select d-flex h-100 rounded-start-0 rounded-end-0 select2-this input-posthead" id="choice_label_expense_{{forloop.counter}}"
            {% if LANGUAGE_CODE == 'ja'%}
            placeholder="費用" 
            {% else %}
            placeholder="Expense" 
            {% endif %}
            oninput="onUpdateCorresponding('{{forloop.counter}}');"
            {% if choice.label_expense %}
                value="{{choice.label_expense}}"
            {% elif forloop.first %}
                {% for category in related_custom_property_choices %}
                    {% if forloop.first %}value="{{category.value}}"{% endif %}
                {% endfor %}
            {% endif %}
        >
        
            {% for category in related_custom_property_choices %}
                <option {% if choice.label_expense == category.value %}selected{% endif %} value="{{category.value}}">{{category.label|truncatechars:28}}</option>
            {% endfor %}
        </select>
        

            <input hidden class="sub-choice-value" name="choice_value" id="choice-value-{{forloop.counter}}" value='{{choice.refered_choice}}'/>
            <input hidden class="sub-choice-label" name="choice_label" id="choice-label-{{forloop.counter}}" value='{{choice.label_income}};{{choice.label_expense}}'/>
        {% else %}
            <input required class="sub-choice-label form-control d-flex w-50 h-100 input-prehead" id="choice-label-{{forloop.counter}}" name="choice_label"
                {% if LANGUAGE_CODE == 'ja'%}
                placeholder="ラベル" 
                {% else %}
                placeholder="Label" 
                {% endif %}
                value = '{{choice.label}}'
                oninput="onChangeChoiceOption(this);"
            />
        

            <input id="choice-value-{{forloop.counter}}"{% if choice %}readonly{% endif%} required class="sub-choice-value form-control d-flex me-3 h-100 input-posthead {% if property.id == 'counter_category' %}w-100{% else %}w-50{% endif %}" {% if property.id != 'counter_category' %}name="choice_value"{% endif %}
                {% if LANGUAGE_CODE == 'ja'%}
                placeholder="値" 
                {% else %}
                placeholder="Value" 
                {% endif %}
                {% if property.id == 'counter_category' %}
                value = '{{choice.value|truncatechars:28}}'
                {% else %}
                value = '{{choice.value}}'
                {% endif %}
                oninput="onChangeChoiceOption(this);"
            />

            <input type="color" class="form-control form-control-color" style="width: 60px; height: 40px;" id="choice-color-{{forloop.counter}}" name="choice_color"
                {% if LANGUAGE_CODE == 'ja'%}
                title="色を選択"
                {% else %}
                title="Choose color"
                {% endif %}
                value="{{choice.color|default:'#000000'}}"
                oninput="onChangeChoiceOption(this);"
            />
        
        {% endif %}

        {% if not property.id == 'counter_category' %}
        <button class="btn btn-danger btn-sm ms-1" onclick='$(this).parent().remove()' type="button">X</button>
        {% endif %}
    </div>
    
    <label class="warning-label" style="display: none; color: red;"></label>
</div>

<script>
    onUpdateCorresponding('{{forloop.counter}}');
</script>