{% load tz %}
{% load custom_tags %}
{% load i18n %}
{% load hosts %}
{% load humanize %}
{% load formatting_tags %}
{% get_current_language as LANGUAGE_CODE %}

{% if CustomFieldName.type == 'bill_objects' %}
    {% include "data/common/custom_field/bill/row-partial.html" with obj_id=obj_id %}

{% elif CustomFieldName.type == 'warehouse_objects' %}
    {% include "data/common/custom_field/warehouse/row-partial.html" with obj_id=obj_id %}

{% elif CustomFieldName.type == 'purchase_order' %}
    {% include "data/common/custom_field/purchase_order/row-partial.html" with obj_id=obj_id %}
{% elif CustomFieldName.type == 'date' %}
    {% date_format value %}
{% elif CustomFieldName.type == 'date_time' %}
    {% date_format value 1 %}

{% elif CustomFieldName.type == 'image' %}
    {% if value %}
    <div class="ms-2">
        <div class="symbol symbol-lg-50px symbol-50px">
            <img style="object-fit: contain;" alt="Pic" src="{{value.file.url}}">
            </img>
        </div>
    </div>
    {% endif %}

{% elif CustomFieldName.type == 'file' %}
    {% if value %}
        <a id='download_file_property' target='_blank' class="text-primary" href="{{value}}">
            {% if LANGUAGE_CODE == 'ja'%}ダウンロード{% else %}Download{% endif %}
        </a>
    {% endif %}

{% elif CustomFieldName.type == 'tag' %}
    {% if value|is_json_string %}
        {% for tag in value|get_json %}
            <div class="fw-bold mb-1">
                <span class="badge bg-gray-500">{{tag.value}}</span>
            </div> 
        {% endfor %}
    {% endif %}

{% elif CustomFieldName.type == 'number' %}
    <div class="fw-bold">
        
        {% get_custom_field_value_object CustomFieldName.id obj_id object_type as custom_field_value %}
        {% if custom_field_value.value %}
            {% with custom_field_value.field_name as CustomFieldName %}
                {% if CustomFieldName.number_format == '%' %}
                    {{custom_field_value.value|floatformat:"-2"|intcomma:False}} {{CustomFieldName.number_format|upper}}
                {% elif CustomFieldName.number_format == 'number' %}
                    {{custom_field_value.value|floatformat:"-2"|intcomma:False}}
                {% else %}
                    {{CustomFieldName.number_format|upper|get_currency_symbol}} {{custom_field_value.value|use_thousand_separator_string_with_currency:CustomFieldName.number_format}}
                {% endif %}
            {% endwith %}
        {% endif %}
    </div>
{% elif CustomFieldName.type == 'order_objects' %}
    <div class="fw-bold">
        {% get_custom_field_value_object CustomFieldName.id obj_id object_type as custom_field_value %}
        {% if custom_field_value.value %}
            {% with order_obj=custom_field_value.value|get_order%}
                {% if order_obj %}
                <a id="profile_wizard_button" class="text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer manage-full-wizard-button order_{{order.id}}" 
                    hx-get="{% host_url 'load_manage_order_drawer' host 'app' %}"
                    hx-vals = '{"drawer_type":"manage-orders", "order_id":"{{order_obj.id}}","view_id":"{{view_id}}", "page": "{{page}}"}'
                    hx-target="#manage-full-drawer-content"
                    hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content" 
                    hx-trigger="click"
                >
                    #{{order_obj.order_id|stringformat:"04d"}}
                </a>
                    
                {% endif %}
            {% endwith %}
        {% endif %}
    </div>
{% elif CustomFieldName.type == 'components' %}
    <div class="fw-bold">
        {% if value %}
            {% with obj_id=value|split:','|first %}
                {% if obj_id|is_uuid %}
                    <div>
                        {% with object_type|get_object:obj_id as obj %}
                            {% get_object_display obj object_type %}
                        {% endwith %}
                    </div>
                    {% with total_obj=value|split:','|length %}
                        {% if total_obj > 1 %}
                        <div>
                            {% if LANGUAGE_CODE == 'ja' %}
                                +{{total_obj|add:"-1"}} さらに表示
                            {% else %}
                                +{{total_obj|add:"-1"}} more
                            {% endif %}
                        </div>
                        {% endif %}
                    {% endwith %}
                {% endif %}
            {% endwith %}
        {% endif %} 
    </div>
{% elif CustomFieldName.type == 'hierarchy' %}
<div class="fw-bold">

    {% get_custom_field_value_hierarchy CustomFieldName.id obj_id as custom_field_values %}
    {% for custom_field_value in custom_field_values %}
        {% if custom_field_value.value %}
        <div>
        {{custom_field_value.value|get_hierarchy_display_column}}

        {% for company in custom_field_value.property_parent.all%}
        <div>
        - {{company.company_id|stringformat:"04d"}} {{company.name}}
        </div>
        {% endfor %}
        </div>
        {% endif %}
    {% endfor %}

 </div>
{% elif CustomFieldName.type == 'choice' %}
<div class="fw-bold">

    {% if value %}
        {% if CustomFieldName.choice_value%}
            {% for choice in CustomFieldName.choice_value|string_list_to_list %}
                {% if choice.value == value %}
                    <span class="d-inline-flex align-items-center">
                        <span class="me-2" style="background-color: {{ choice.color|default:'#000000' }}; width: 16px; height: 16px; border-radius: 50%; display: inline-block; border: 1px solid #ccc;"></span>
                        {{ choice.label }}
                    </span>
                {% endif %}
            {% endfor %}
        {% elif CustomFieldName.color %}
        <span class="d-inline-flex align-items-center">
            <span class="me-2" style="background-color: {{ CustomFieldName.color|default:'#000000' }}; width: 16px; height: 16px; border-radius: 50%; display: inline-block; border: 1px solid #ccc;"></span>
            {{ value }}
        </span>

        {% else %}
            {{ value }}
        {% endif %}

    {% endif %}
</div>
{% elif value is not None %}
    {{ value }}
{% endif %}

